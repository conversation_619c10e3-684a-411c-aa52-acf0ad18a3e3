-- =============================================
-- 查询剩余测试菜单脚本
-- 用于查找未被清理的测试菜单项
-- =============================================

USE [EnterpriseManagementSystem]
GO

PRINT '查询当前所有菜单项...'
PRINT '========================================'

-- 查询所有菜单项，特别关注可能的测试菜单
SELECT 
    m.Id,
    m.Code AS '菜单代码',
    m.Name AS '菜单名称',
    m.RouteUrl AS '路由地址',
    m.Icon AS '图标',
    m.Level AS '层级',
    m.SortOrder AS '排序',
    CASE m.IsEnabled 
        WHEN 1 THEN '启用' 
        ELSE '禁用' 
    END AS '状态',
    CASE m.IsSystem 
        WHEN 1 THEN '是' 
        ELSE '否' 
    END AS '系统菜单',
    m.CreatedAt AS '创建时间'
FROM MenuItems m
ORDER BY m.Level, m.SortOrder, m.Name;

PRINT ''
PRINT '查找可能的测试菜单（根据名称模式）：'

-- 根据名称模式查找可能的测试菜单
SELECT 
    m.Id,
    m.Code AS '菜单代码',
    m.Name AS '菜单名称',
    m.RouteUrl AS '路由地址'
FROM MenuItems m
WHERE 
    m.Name LIKE '%测试%' 
    OR m.Name LIKE '%调试%' 
    OR m.Name LIKE '%修复%'
    OR m.Name LIKE '%工作流%'
    OR m.Name LIKE '%权限%'
    OR m.Name LIKE '%部门%'
    OR m.Code LIKE '%Test%'
    OR m.Code LIKE '%Debug%'
    OR m.Code LIKE '%Fix%'
    OR m.Code LIKE '%Workflow%'
ORDER BY m.Name;

PRINT ''
PRINT '查找特定的剩余菜单：'

-- 查找特定的剩余菜单
SELECT 
    m.Id,
    m.Code AS '菜单代码',
    m.Name AS '菜单名称',
    m.RouteUrl AS '路由地址'
FROM MenuItems m
WHERE 
    m.Name IN (
        '修复维修权限',
        '测试维修部门', 
        '工作流历史测试'
    )
ORDER BY m.Name;

GO
