-- =============================================
-- 强制清理特定菜单脚本
-- 直接按ID或名称强制删除特定的测试菜单
-- =============================================

USE [EnterpriseManagementSystem]
GO

PRINT '开始强制清理特定测试菜单...'
PRINT '========================================'

-- 首先查看这些特定菜单的详细信息
PRINT '查看目标菜单的详细信息：'
SELECT 
    m.Id,
    m.Code AS '菜单代码',
    m.Name AS '菜单名称',
    m.RouteUrl AS '路由地址',
    m.ParentId AS '父菜单ID',
    m.Level AS '层级',
    m.SortOrder AS '排序'
FROM MenuItems m
WHERE 
    m.Name = '修复维修权限'
    OR m.Name = '测试维修部门'
    OR m.Name = '工作流历史测试'
ORDER BY m.Name;

-- 检查是否有子菜单
PRINT ''
PRINT '检查是否有子菜单：'
SELECT 
    child.Id AS '子菜单ID',
    child.Name AS '子菜单名称',
    parent.Name AS '父菜单名称'
FROM MenuItems child
INNER JOIN MenuItems parent ON child.ParentId = parent.Id
WHERE 
    parent.Name = '修复维修权限'
    OR parent.Name = '测试维修部门'
    OR parent.Name = '工作流历史测试';

-- 开始强制删除
BEGIN TRANSACTION;

BEGIN TRY
    DECLARE @DeletedCount INT = 0;
    
    -- 先删除可能的子菜单
    DELETE child 
    FROM MenuItems child
    INNER JOIN MenuItems parent ON child.ParentId = parent.Id
    WHERE 
        parent.Name = '修复维修权限'
        OR parent.Name = '测试维修部门'
        OR parent.Name = '工作流历史测试';
    
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    -- 删除主菜单 - 方法1：按确切名称
    DELETE FROM MenuItems 
    WHERE Name = '修复维修权限';
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    DELETE FROM MenuItems 
    WHERE Name = '测试维修部门';
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    DELETE FROM MenuItems 
    WHERE Name = '工作流历史测试';
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    -- 删除主菜单 - 方法2：按可能的代码
    DELETE FROM MenuItems 
    WHERE Code IN (
        'FixMaintenancePermission',
        'TestMaintenanceDepartment', 
        'WorkflowHistoryTest'
    );
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    -- 删除主菜单 - 方法3：按可能的路由
    DELETE FROM MenuItems 
    WHERE RouteUrl IN (
        'fix-maintenance-permission',
        'test-maintenance-department',
        'workflow-history-test'
    );
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    COMMIT TRANSACTION;
    
    PRINT ''
    PRINT '强制清理完成！'
    PRINT '总共删除了 ' + CAST(@DeletedCount AS NVARCHAR(10)) + ' 个菜单项'
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    
    PRINT ''
    PRINT '强制清理失败：' + ERROR_MESSAGE()
    PRINT '可能需要手动删除这些菜单'
    
END CATCH

-- 最终验证
PRINT ''
PRINT '最终验证结果：'

-- 检查这三个特定菜单是否还存在
SELECT 
    COUNT(*) AS '剩余目标菜单数量'
FROM MenuItems m
WHERE 
    m.Name = '修复维修权限'
    OR m.Name = '测试维修部门'
    OR m.Name = '工作流历史测试';

-- 如果还有，显示它们的ID以便手动删除
DECLARE @RemainingMenus TABLE (Id INT, Name NVARCHAR(100))
INSERT INTO @RemainingMenus
SELECT Id, Name
FROM MenuItems m
WHERE 
    m.Name = '修复维修权限'
    OR m.Name = '测试维修部门'
    OR m.Name = '工作流历史测试';

IF EXISTS (SELECT 1 FROM @RemainingMenus)
BEGIN
    PRINT ''
    PRINT '⚠ 以下菜单仍然存在，请手动删除：'
    
    DECLARE @MenuId INT, @MenuName NVARCHAR(100);
    DECLARE menu_cursor CURSOR FOR 
    SELECT Id, Name FROM @RemainingMenus;
    
    OPEN menu_cursor;
    FETCH NEXT FROM menu_cursor INTO @MenuId, @MenuName;
    
    WHILE @@FETCH_STATUS = 0
    BEGIN
        PRINT 'DELETE FROM MenuItems WHERE Id = ' + CAST(@MenuId AS NVARCHAR(10)) + '; -- ' + @MenuName;
        FETCH NEXT FROM menu_cursor INTO @MenuId, @MenuName;
    END
    
    CLOSE menu_cursor;
    DEALLOCATE menu_cursor;
END
ELSE
BEGIN
    PRINT '✓ 所有目标菜单已成功删除！'
END

-- 显示最终的菜单列表
PRINT ''
PRINT '最终菜单列表：'
SELECT 
    m.Name AS '菜单名称',
    m.RouteUrl AS '路由地址',
    CASE m.Level
        WHEN 1 THEN '一级菜单'
        WHEN 2 THEN '二级菜单'
        ELSE '多级菜单'
    END AS '层级'
FROM MenuItems m
WHERE m.IsEnabled = 1
ORDER BY m.Level, m.SortOrder, m.Name;

PRINT ''
PRINT '========================================'
PRINT '强制清理完成！'
PRINT ''
PRINT '如果菜单仍然显示在界面上，请：'
PRINT '1. 重启应用程序'
PRINT '2. 清理浏览器缓存'
PRINT '3. 检查菜单缓存机制'

GO
