-- =============================================
-- 企业管理系统 - 完整数据库脚本
-- 包含权限管理和设备管理模块
-- 包含零件申请表（无审批流程）
-- 支持明文密码存储（开发环境）
-- 兼容 SQL Server 2012+
-- 最后更新：2025-01-08 - 合并零件申请表重建脚本
-- =============================================

USE [master]
GO

-- 删除现有数据库（如果存在）- 重建数据库
IF EXISTS (SELECT name FROM sys.databases WHERE name = N'EnterpriseManagementSystem')
BEGIN
    PRINT '删除现有数据库...'
    ALTER DATABASE [EnterpriseManagementSystem] SET SINGLE_USER WITH ROLLBACK IMMEDIATE
    DROP DATABASE [EnterpriseManagementSystem]
    PRINT '数据库删除完成'
END
GO

-- 创建数据库
PRINT '创建新数据库...'
CREATE DATABASE [EnterpriseManagementSystem]
PRINT '数据库创建完成'
GO

USE [EnterpriseManagementSystem]
GO

-- =============================================
-- 1. 创建表结构
-- =============================================

-- 用户表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Users](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Username] [nvarchar](50) NOT NULL,
        [PasswordHash] [nvarchar](255) NOT NULL,  -- 明文密码存储
        [DisplayName] [nvarchar](100) NOT NULL,
        [Email] [nvarchar](100) NULL,
        [Phone] [nvarchar](20) NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [IsLocked] [bit] NOT NULL DEFAULT(0),
        [LoginFailureCount] [int] NOT NULL DEFAULT(0),
        [LastLoginTime] [datetime2] NULL,
        [LastLoginIp] [nvarchar](50) NULL,
        [LockedAt] [datetime2] NULL,
        [LockReason] [nvarchar](500) NULL,
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [UpdatedAt] [datetime2] NULL,
        [Remark] [nvarchar](500) NULL,
        CONSTRAINT [PK_Users] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
END
GO

-- 角色表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Roles' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Roles](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [SortOrder] [int] NOT NULL DEFAULT(0),
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [IsSystem] [bit] NOT NULL DEFAULT(0),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_Roles] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
END
GO

-- 权限表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Permissions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Permissions](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](100) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [Module] [nvarchar](50) NOT NULL,
        [Action] [nvarchar](50) NOT NULL,
        [ParentId] [int] NULL,
        [Level] [int] NOT NULL DEFAULT(2), -- 1=菜单级, 2=页面级, 3=功能级, 4=数据级
        [SortOrder] [int] NOT NULL DEFAULT(0),
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [IsSystem] [bit] NOT NULL DEFAULT(0),
        [RouteUrl] [nvarchar](200) NULL,
        [Icon] [nvarchar](100) NULL,
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_Permissions] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
END
GO

-- 用户角色关联表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserRoles' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[UserRoles](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [UserId] [int] NOT NULL,
        [RoleId] [int] NOT NULL,
        [AssignedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [AssignedBy] [int] NULL,
        [ExpiresAt] [datetime2] NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [IsDeleted] [bit] NOT NULL DEFAULT(0),
        [Remark] [nvarchar](500) NULL,
        CONSTRAINT [PK_UserRoles] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_UserRoles_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_UserRoles_Roles] FOREIGN KEY([RoleId]) REFERENCES [dbo].[Roles] ([Id])
    )
END
GO

-- 角色权限关联表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RolePermissions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[RolePermissions](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [RoleId] [int] NOT NULL,
        [PermissionId] [int] NOT NULL,
        [AssignedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [AssignedBy] [int] NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [Remark] [nvarchar](500) NULL,
        CONSTRAINT [PK_RolePermissions] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RolePermissions_Roles] FOREIGN KEY([RoleId]) REFERENCES [dbo].[Roles] ([Id]),
        CONSTRAINT [FK_RolePermissions_Permissions] FOREIGN KEY([PermissionId]) REFERENCES [dbo].[Permissions] ([Id])
    )
END
GO

-- 用户直接权限关联表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserPermissions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[UserPermissions](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [UserId] [int] NOT NULL,
        [PermissionId] [int] NOT NULL,
        [IsGranted] [bit] NOT NULL DEFAULT(1), -- 1=授权, 0=拒绝
        [AssignedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [AssignedBy] [int] NULL,
        [ExpiresAt] [datetime2] NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [Remark] [nvarchar](500) NULL,
        CONSTRAINT [PK_UserPermissions] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_UserPermissions_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_UserPermissions_Permissions] FOREIGN KEY([PermissionId]) REFERENCES [dbo].[Permissions] ([Id])
    )
END
GO

-- 登录日志表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LoginLogs' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[LoginLogs](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Username] [nvarchar](50) NOT NULL,
        [ClientIP] [nvarchar](50) NULL,
        [UserAgent] [nvarchar](500) NULL,
        [IsSuccess] [bit] NOT NULL,
        [FailureReason] [nvarchar](500) NULL,
        [LoginTime] [datetime2] NOT NULL DEFAULT(GETDATE()),
        CONSTRAINT [PK_LoginLogs] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
END
GO

-- 菜单项表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MenuItems' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MenuItems](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [RouteUrl] [nvarchar](200) NULL,
        [Icon] [nvarchar](100) NULL,
        [ParentId] [int] NULL,
        [Level] [int] NOT NULL DEFAULT(1),
        [SortOrder] [int] NOT NULL DEFAULT(0),
        [PermissionCode] [nvarchar](100) NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [IsPublic] [bit] NOT NULL DEFAULT(0),
        [IsSystem] [bit] NOT NULL DEFAULT(0),
        [MenuType] [int] NOT NULL DEFAULT(1), -- 1=菜单项, 2=分组标题
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_MenuItems] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_MenuItems_Parent] FOREIGN KEY([ParentId]) REFERENCES [dbo].[MenuItems] ([Id])
    )
END
GO

-- 部门类型表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DepartmentTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[DepartmentTypes](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [SortOrder] [int] NOT NULL DEFAULT(0),
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [UpdatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        CONSTRAINT [PK_DepartmentTypes] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UK_DepartmentTypes_Code] UNIQUE NONCLUSTERED ([Code] ASC)
    )
END
GO

-- 工种类型表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='JobTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[JobTypes](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [Category] [nvarchar](50) NOT NULL, -- 工种分类：生产、维修、管理等
        [SortOrder] [int] NOT NULL DEFAULT(0),
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [UpdatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        CONSTRAINT [PK_JobTypes] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [UK_JobTypes_Code] UNIQUE NONCLUSTERED ([Code] ASC)
    )
END
GO

-- 用户工种关联表（多对多关系）
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserJobTypes' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[UserJobTypes](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [UserId] [int] NOT NULL,
        [JobTypeId] [int] NOT NULL,
        [IsPrimary] [bit] NOT NULL DEFAULT(0), -- 是否为主要工种
        [SkillLevel] [int] NOT NULL DEFAULT(1), -- 熟练程度（1-5级）
        [AcquiredAt] [datetime2] NOT NULL DEFAULT(GETDATE()), -- 获得时间
        [AssignedBy] [int] NULL, -- 分配人ID
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [UpdatedAt] [datetime2] NULL,
        [Remark] [nvarchar](500) NULL,
        CONSTRAINT [PK_UserJobTypes] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_UserJobTypes_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_UserJobTypes_JobTypes] FOREIGN KEY([JobTypeId]) REFERENCES [dbo].[JobTypes] ([Id]),
        CONSTRAINT [UK_UserJobTypes_User_JobType] UNIQUE NONCLUSTERED ([UserId] ASC, [JobTypeId] ASC)
    )
END
GO

-- 部门表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Departments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Departments](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Description] [nvarchar](500) NULL,
        [DepartmentTypeId] [int] NULL,
        [ParentId] [int] NULL,
        [Level] [int] NOT NULL DEFAULT(1),
        [SortOrder] [int] NOT NULL DEFAULT(0),
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_Departments] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_Departments_Parent] FOREIGN KEY([ParentId]) REFERENCES [dbo].[Departments] ([Id])
    )
END
GO

-- 设备型号表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EquipmentModels' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[EquipmentModels](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [Category] [nvarchar](50) NOT NULL,
        [Brand] [nvarchar](100) NULL,
        [Model] [nvarchar](100) NULL,
        [Specifications] [nvarchar](1000) NULL,
        [Description] [nvarchar](500) NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_EquipmentModels] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
END
GO

-- 位置表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Locations' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Locations](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [DepartmentId] [int] NOT NULL,
        [ParentId] [int] NULL,
        [Level] [int] NOT NULL DEFAULT(1),
        [Address] [nvarchar](200) NULL,
        [Description] [nvarchar](500) NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_Locations] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_Locations_Departments] FOREIGN KEY([DepartmentId]) REFERENCES [dbo].[Departments] ([Id]),
        CONSTRAINT [FK_Locations_Parent] FOREIGN KEY([ParentId]) REFERENCES [dbo].[Locations] ([Id])
    )
END
GO

-- 设备表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Equipment' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Equipment](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [Code] [nvarchar](50) NOT NULL,
        [Name] [nvarchar](100) NOT NULL,
        [DepartmentId] [int] NOT NULL,
        [LocationId] [int] NOT NULL,
        [ModelId] [int] NOT NULL,
        [SerialNumber] [nvarchar](100) NULL,
        [AssetNumber] [nvarchar](100) NULL,
        [PurchaseDate] [datetime2] NULL,
        [WarrantyExpiry] [datetime2] NULL,
        [Status] [int] NOT NULL DEFAULT(1),
        [LastMaintenanceDate] [datetime2] NULL,
        [NextMaintenanceDate] [datetime2] NULL,
        [Description] [nvarchar](500) NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_Equipment] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_Equipment_Departments] FOREIGN KEY([DepartmentId]) REFERENCES [dbo].[Departments] ([Id]),
        CONSTRAINT [FK_Equipment_Locations] FOREIGN KEY([LocationId]) REFERENCES [dbo].[Locations] ([Id]),
        CONSTRAINT [FK_Equipment_EquipmentModels] FOREIGN KEY([ModelId]) REFERENCES [dbo].[EquipmentModels] ([Id])
    )
END
GO

-- 报修单表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RepairOrders' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[RepairOrders](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [OrderNumber] [nvarchar](50) NOT NULL,
        [EquipmentId] [int] NOT NULL,
        [ReporterId] [int] NOT NULL,
        [FaultDescription] [nvarchar](1000) NOT NULL,
        [UrgencyLevel] [int] NOT NULL DEFAULT(2),
        [MaintenanceDepartmentId] [int] NOT NULL,
        [Status] [int] NOT NULL DEFAULT(1), -- 1=待处理,2=处理中,3=已完成,4=已作废,5=已关闭,6=待确认
        [ReportedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [AssignedTo] [int] NULL,
        [AssignedAt] [datetime2] NULL,
        [StartedAt] [datetime2] NULL,
        [CompletedAt] [datetime2] NULL,
        [CancelledAt] [datetime2] NULL,
        [CancelReason] [nvarchar](500) NULL,
        [RepairDescription] [nvarchar](1000) NULL,
        [RepairCost] [decimal](18,2) NULL,
        [PartsUsed] [nvarchar](1000) NULL,
        [TestResult] [nvarchar](500) NULL,
        [ReporterRating] [int] NULL,
        [ReporterComment] [nvarchar](500) NULL,
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [UpdatedAt] [datetime2] NULL,
        [Remark] [nvarchar](1000) NULL,
        CONSTRAINT [PK_RepairOrders] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RepairOrders_Equipment] FOREIGN KEY([EquipmentId]) REFERENCES [dbo].[Equipment] ([Id]),
        CONSTRAINT [FK_RepairOrders_MaintenanceDepartments] FOREIGN KEY([MaintenanceDepartmentId]) REFERENCES [dbo].[Departments] ([Id]),
        CONSTRAINT [FK_RepairOrders_Reporter] FOREIGN KEY([ReporterId]) REFERENCES [dbo].[Users] ([Id])
    )
END
GO

-- 报修单附件表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RepairOrderAttachments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[RepairOrderAttachments](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [RepairOrderId] [int] NOT NULL,
        [FileName] [nvarchar](255) NOT NULL,
        [OriginalFileName] [nvarchar](255) NOT NULL,
        [FilePath] [nvarchar](500) NOT NULL,
        [FileSize] [bigint] NOT NULL,
        [ContentType] [nvarchar](100) NOT NULL,
        [AttachmentType] [int] NOT NULL DEFAULT(1),
        [UploadedBy] [int] NOT NULL,
        [UploadedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [Description] [nvarchar](500) NULL,
        CONSTRAINT [PK_RepairOrderAttachments] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RepairOrderAttachments_RepairOrders] FOREIGN KEY([RepairOrderId]) REFERENCES [dbo].[RepairOrders] ([Id])
    )
END
GO

-- 零件申请表（无审批流程）
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RepairOrderPartRequests' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[RepairOrderPartRequests](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [RepairOrderId] [int] NOT NULL,                 -- 关联的维修单ID
        [PartName] [nvarchar](100) NOT NULL,            -- 零件名称
        [Specification] [nvarchar](200) NULL,           -- 规格型号
        [RequestedQuantity] [int] NOT NULL DEFAULT(1),  -- 申请数量
        [Unit] [nvarchar](20) NOT NULL DEFAULT('个'),   -- 计量单位
        [Reason] [nvarchar](500) NULL,                  -- 更换原因
        [Remark] [nvarchar](1000) NULL,                 -- 备注
        [Status] [int] NOT NULL DEFAULT(1),             -- 状态：1=申请中,2=已领用,3=已安装,4=已取消
        [RequestedBy] [int] NOT NULL,                   -- 申请人ID
        [RequestedAt] [datetime2] NOT NULL DEFAULT(GETDATE()), -- 申请时间

        -- 外部系统集成预留字段
        [ExternalPartNumber] [nvarchar](50) NULL,       -- 外部系统零件编号
        [ExternalRequisitionDetailId] [nvarchar](50) NULL, -- 外部系统领用单明细ID
        [ActualQuantity] [int] NULL,                    -- 实际领用数量
        [ActualPartName] [nvarchar](100) NULL,          -- 实际领用名称
        [ActualSpecification] [nvarchar](200) NULL,     -- 实际领用规格

        -- 处理信息（无审批环节）
        [IssuedBy] [int] NULL,                          -- 发放人ID
        [IssuedAt] [datetime2] NULL,                    -- 发放时间
        [InstalledBy] [int] NULL,                       -- 安装人ID
        [InstalledAt] [datetime2] NULL,                 -- 安装时间

        -- 成本信息
        [UnitPrice] [decimal](18,2) NULL,               -- 单价
        [TotalCost] [decimal](18,2) NULL,               -- 总成本
        [WarehouseOrderNumber] [nvarchar](50) NULL,     -- 仓库出库单号

        -- 系统字段
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [UpdatedAt] [datetime2] NULL,
        [CreatedBy] [int] NULL,
        [UpdatedBy] [int] NULL,

        CONSTRAINT [PK_RepairOrderPartRequests] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RepairOrderPartRequests_RepairOrders] FOREIGN KEY([RepairOrderId]) REFERENCES [dbo].[RepairOrders] ([Id]) ON DELETE CASCADE,
        CONSTRAINT [FK_RepairOrderPartRequests_RequestedBy] FOREIGN KEY([RequestedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_IssuedBy] FOREIGN KEY([IssuedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_InstalledBy] FOREIGN KEY([InstalledBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_CreatedBy] FOREIGN KEY([CreatedBy]) REFERENCES [dbo].[Users] ([Id]),
        CONSTRAINT [FK_RepairOrderPartRequests_UpdatedBy] FOREIGN KEY([UpdatedBy]) REFERENCES [dbo].[Users] ([Id])
    )
END
GO

-- =============================================
-- 1.5. 更新现有表结构（添加缺失字段）
-- =============================================

-- 更新Roles表，添加缺失字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Roles]') AND name = 'CreatedBy')
    ALTER TABLE [dbo].[Roles] ADD [CreatedBy] [int] NULL
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Roles]') AND name = 'UpdatedBy')
    ALTER TABLE [dbo].[Roles] ADD [UpdatedBy] [int] NULL
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Roles]') AND name = 'Remark')
    ALTER TABLE [dbo].[Roles] ADD [Remark] [nvarchar](1000) NULL
GO

-- 更新Permissions表，添加缺失字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND name = 'ParentId')
    ALTER TABLE [dbo].[Permissions] ADD [ParentId] [int] NULL
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND name = 'Icon')
    ALTER TABLE [dbo].[Permissions] ADD [Icon] [nvarchar](100) NULL
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND name = 'CreatedBy')
    ALTER TABLE [dbo].[Permissions] ADD [CreatedBy] [int] NULL
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND name = 'UpdatedBy')
    ALTER TABLE [dbo].[Permissions] ADD [UpdatedBy] [int] NULL
GO

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Permissions]') AND name = 'Remark')
    ALTER TABLE [dbo].[Permissions] ADD [Remark] [nvarchar](1000) NULL
GO

-- 更新Users表，添加部门关联字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'DepartmentId')
    ALTER TABLE [dbo].[Users] ADD [DepartmentId] [int] NULL
GO

-- 移除Users表的旧字段（如果存在）
-- 移除旧的RoleId字段（现在使用UserRoles表）
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'RoleId')
BEGIN
    -- 先删除外键约束
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Users_Roles')
        ALTER TABLE [dbo].[Users] DROP CONSTRAINT [FK_Users_Roles]

    -- 删除字段
    ALTER TABLE [dbo].[Users] DROP COLUMN [RoleId]
    PRINT '移除Users表的旧RoleId字段'
END
GO

-- 移除Users表的旧工种字段（如果存在）
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'JobTypeId')
BEGIN
    -- 先删除外键约束
    IF EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Users_JobTypes')
        ALTER TABLE [dbo].[Users] DROP CONSTRAINT [FK_Users_JobTypes]

    -- 删除字段
    ALTER TABLE [dbo].[Users] DROP COLUMN [JobTypeId]
    PRINT '移除Users表的旧JobTypeId字段'
END
GO

-- 更新Departments表，添加部门类型字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Departments]') AND name = 'DepartmentTypeId')
    ALTER TABLE [dbo].[Departments] ADD [DepartmentTypeId] [int] NULL
GO

-- 添加外键约束
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Users_Departments')
    ALTER TABLE [dbo].[Users] ADD CONSTRAINT [FK_Users_Departments] FOREIGN KEY([DepartmentId]) REFERENCES [dbo].[Departments] ([Id])
GO

-- 旧的工种外键约束已在上面移除

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_Departments_DepartmentTypes')
    ALTER TABLE [dbo].[Departments] ADD CONSTRAINT [FK_Departments_DepartmentTypes] FOREIGN KEY([DepartmentTypeId]) REFERENCES [dbo].[DepartmentTypes] ([Id])
GO

-- 创建角色部门分配表（简化的多对多关系）
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RoleDepartmentAssignments' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[RoleDepartmentAssignments](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [RoleId] [int] NOT NULL,
        [DepartmentId] [int] NOT NULL,
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](500) NULL,
        CONSTRAINT [PK_RoleDepartmentAssignments] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RoleDepartmentAssignments_Roles] FOREIGN KEY([RoleId]) REFERENCES [dbo].[Roles] ([Id]),
        CONSTRAINT [FK_RoleDepartmentAssignments_Departments] FOREIGN KEY([DepartmentId]) REFERENCES [dbo].[Departments] ([Id]),
        CONSTRAINT [UK_RoleDepartmentAssignments] UNIQUE ([RoleId], [DepartmentId])
    ) ON [PRIMARY]

    PRINT '创建角色部门分配表成功'

    -- 从旧表迁移数据（如果存在）
    IF EXISTS (SELECT * FROM sysobjects WHERE name='RoleDepartmentPermissions' AND xtype='U')
    BEGIN
        PRINT '从 RoleDepartmentPermissions 迁移数据到 RoleDepartmentAssignments...'

        -- 迁移数据，只保留 PermissionType = 1 (可报修设备) 的记录
        INSERT INTO [dbo].[RoleDepartmentAssignments] ([RoleId], [DepartmentId], [IsEnabled], [CreatedAt], [CreatedBy], [Remark])
        SELECT DISTINCT
            rdp.RoleId,
            rdp.DepartmentId,
            rdp.IsEnabled,
            rdp.CreatedAt,
            rdp.CreatedBy,
            N'从RoleDepartmentPermissions迁移'
        FROM RoleDepartmentPermissions rdp
        WHERE rdp.PermissionType = 1 -- 只迁移可报修设备的权限
        AND NOT EXISTS (
            SELECT 1 FROM RoleDepartmentAssignments rda
            WHERE rda.RoleId = rdp.RoleId AND rda.DepartmentId = rdp.DepartmentId
        )

        PRINT '数据迁移完成'
    END
END
GO

-- MaintenancePersonnel 表已移除，维修人员直接从用户工种关系中获取

-- 创建维修部门权限表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MaintenanceDepartmentPermissions' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[MaintenanceDepartmentPermissions](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [MaintenanceDepartmentId] [int] NOT NULL, -- 维修部门ID（维修类型的部门）
        [TargetDepartmentId] [int] NOT NULL, -- 目标部门ID（可以维修的部门）
        [IsEnabled] [bit] NOT NULL DEFAULT(1),
        [CreatedAt] [datetime2] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [int] NULL,
        [UpdatedAt] [datetime2] NULL,
        [UpdatedBy] [int] NULL,
        [Remark] [nvarchar](500) NULL,
        CONSTRAINT [PK_MaintenanceDepartmentPermissions] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_MaintenanceDepartmentPermissions_MaintenanceDepartment] FOREIGN KEY([MaintenanceDepartmentId]) REFERENCES [dbo].[Departments] ([Id]),
        CONSTRAINT [FK_MaintenanceDepartmentPermissions_TargetDepartment] FOREIGN KEY([TargetDepartmentId]) REFERENCES [dbo].[Departments] ([Id]),
        CONSTRAINT [UK_MaintenanceDepartmentPermissions] UNIQUE ([MaintenanceDepartmentId], [TargetDepartmentId])
    ) ON [PRIMARY]

    PRINT '创建维修部门权限表成功'
END
GO



-- =============================================
-- 2. 创建索引
-- =============================================

-- Users表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Username')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Users_Username] ON [dbo].[Users] ([Username] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_Email')
    CREATE NONCLUSTERED INDEX [IX_Users_Email] ON [dbo].[Users] ([Email] ASC)
GO

-- Roles表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Roles_Code')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Roles_Code] ON [dbo].[Roles] ([Code] ASC)
GO

-- Permissions表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Permissions_Code')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Permissions_Code] ON [dbo].[Permissions] ([Code] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Permissions_Module_Action')
    CREATE NONCLUSTERED INDEX [IX_Permissions_Module_Action] ON [dbo].[Permissions] ([Module] ASC, [Action] ASC)
GO

-- UserRoles表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserRoles_UserId_RoleId')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_UserRoles_UserId_RoleId] ON [dbo].[UserRoles] ([UserId] ASC, [RoleId] ASC)
GO

-- RolePermissions表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RolePermissions_RoleId_PermissionId')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_RolePermissions_RoleId_PermissionId] ON [dbo].[RolePermissions] ([RoleId] ASC, [PermissionId] ASC)
GO

-- UserPermissions表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserPermissions_UserId_PermissionId')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_UserPermissions_UserId_PermissionId] ON [dbo].[UserPermissions] ([UserId] ASC, [PermissionId] ASC)
GO

-- LoginLogs表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_LoginLogs_Username_LoginTime')
    CREATE NONCLUSTERED INDEX [IX_LoginLogs_Username_LoginTime] ON [dbo].[LoginLogs] ([Username] ASC, [LoginTime] DESC)
GO

-- MenuItems表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MenuItems_Code')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_MenuItems_Code] ON [dbo].[MenuItems] ([Code] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MenuItems_ParentId_SortOrder')
    CREATE NONCLUSTERED INDEX [IX_MenuItems_ParentId_SortOrder] ON [dbo].[MenuItems] ([ParentId] ASC, [SortOrder] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MenuItems_PermissionCode')
    CREATE NONCLUSTERED INDEX [IX_MenuItems_PermissionCode] ON [dbo].[MenuItems] ([PermissionCode] ASC)
GO

-- 设备管理相关表索引
-- Departments表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Departments_Code')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Departments_Code] ON [dbo].[Departments] ([Code] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Departments_ParentId_SortOrder')
    CREATE NONCLUSTERED INDEX [IX_Departments_ParentId_SortOrder] ON [dbo].[Departments] ([ParentId] ASC, [SortOrder] ASC)
GO

-- EquipmentModels表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_EquipmentModels_Code')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_EquipmentModels_Code] ON [dbo].[EquipmentModels] ([Code] ASC)
GO

-- Locations表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Locations_Code')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Locations_Code] ON [dbo].[Locations] ([Code] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Locations_DepartmentId')
    CREATE NONCLUSTERED INDEX [IX_Locations_DepartmentId] ON [dbo].[Locations] ([DepartmentId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Locations_ParentId')
    CREATE NONCLUSTERED INDEX [IX_Locations_ParentId] ON [dbo].[Locations] ([ParentId] ASC)
GO

-- Equipment表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Equipment_Code')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Equipment_Code] ON [dbo].[Equipment] ([Code] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Equipment_DepartmentId')
    CREATE NONCLUSTERED INDEX [IX_Equipment_DepartmentId] ON [dbo].[Equipment] ([DepartmentId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Equipment_LocationId')
    CREATE NONCLUSTERED INDEX [IX_Equipment_LocationId] ON [dbo].[Equipment] ([LocationId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Equipment_ModelId')
    CREATE NONCLUSTERED INDEX [IX_Equipment_ModelId] ON [dbo].[Equipment] ([ModelId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Equipment_Status')
    CREATE NONCLUSTERED INDEX [IX_Equipment_Status] ON [dbo].[Equipment] ([Status] ASC)
GO

-- RepairOrders表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrders_OrderNumber')
    CREATE UNIQUE NONCLUSTERED INDEX [IX_RepairOrders_OrderNumber] ON [dbo].[RepairOrders] ([OrderNumber] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrders_EquipmentId')
    CREATE NONCLUSTERED INDEX [IX_RepairOrders_EquipmentId] ON [dbo].[RepairOrders] ([EquipmentId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrders_Status_ReportedAt')
    CREATE NONCLUSTERED INDEX [IX_RepairOrders_Status_ReportedAt] ON [dbo].[RepairOrders] ([Status] ASC, [ReportedAt] DESC)
GO

-- RepairOrderAttachments表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrderAttachments_RepairOrderId')
    CREATE NONCLUSTERED INDEX [IX_RepairOrderAttachments_RepairOrderId] ON [dbo].[RepairOrderAttachments] ([RepairOrderId] ASC)
GO

-- RepairOrderPartRequests表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrderPartRequests_RepairOrderId')
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_RepairOrderId] ON [dbo].[RepairOrderPartRequests] ([RepairOrderId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrderPartRequests_Status')
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_Status] ON [dbo].[RepairOrderPartRequests] ([Status] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrderPartRequests_RequestedAt')
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_RequestedAt] ON [dbo].[RepairOrderPartRequests] ([RequestedAt] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrderPartRequests_ExternalPartNumber')
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalPartNumber] ON [dbo].[RepairOrderPartRequests] ([ExternalPartNumber] ASC) WHERE [ExternalPartNumber] IS NOT NULL
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RepairOrderPartRequests_ExternalRequisitionDetailId')
    CREATE NONCLUSTERED INDEX [IX_RepairOrderPartRequests_ExternalRequisitionDetailId] ON [dbo].[RepairOrderPartRequests] ([ExternalRequisitionDetailId] ASC) WHERE [ExternalRequisitionDetailId] IS NOT NULL
GO

-- RoleDepartmentAssignments表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RoleDepartmentAssignments_RoleId')
    CREATE NONCLUSTERED INDEX [IX_RoleDepartmentAssignments_RoleId] ON [dbo].[RoleDepartmentAssignments] ([RoleId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RoleDepartmentAssignments_DepartmentId')
    CREATE NONCLUSTERED INDEX [IX_RoleDepartmentAssignments_DepartmentId] ON [dbo].[RoleDepartmentAssignments] ([DepartmentId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_RoleDepartmentAssignments_IsEnabled')
    CREATE NONCLUSTERED INDEX [IX_RoleDepartmentAssignments_IsEnabled] ON [dbo].[RoleDepartmentAssignments] ([IsEnabled] ASC)
GO

-- MaintenancePersonnel表已移除，维修人员直接从用户工种关系中获取
-- 相关索引已不需要

-- MaintenanceDepartmentPermissions表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MaintenanceDepartmentPermissions_MaintenanceDepartmentId')
    CREATE NONCLUSTERED INDEX [IX_MaintenanceDepartmentPermissions_MaintenanceDepartmentId] ON [dbo].[MaintenanceDepartmentPermissions] ([MaintenanceDepartmentId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_MaintenanceDepartmentPermissions_TargetDepartmentId')
    CREATE NONCLUSTERED INDEX [IX_MaintenanceDepartmentPermissions_TargetDepartmentId] ON [dbo].[MaintenanceDepartmentPermissions] ([TargetDepartmentId] ASC)
GO



-- Users表部门索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Users_DepartmentId')
    CREATE NONCLUSTERED INDEX [IX_Users_DepartmentId] ON [dbo].[Users] ([DepartmentId] ASC)
GO

-- UserJobTypes表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserJobTypes_UserId')
    CREATE NONCLUSTERED INDEX [IX_UserJobTypes_UserId] ON [dbo].[UserJobTypes] ([UserId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserJobTypes_JobTypeId')
    CREATE NONCLUSTERED INDEX [IX_UserJobTypes_JobTypeId] ON [dbo].[UserJobTypes] ([JobTypeId] ASC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_UserJobTypes_IsPrimary')
    CREATE NONCLUSTERED INDEX [IX_UserJobTypes_IsPrimary] ON [dbo].[UserJobTypes] ([IsPrimary] ASC)
GO



-- =============================================
-- 3. 初始化权限数据
-- =============================================

-- 清空现有数据（可选）
-- DELETE FROM UserPermissions
-- DELETE FROM RolePermissions  
-- DELETE FROM UserRoles
-- DELETE FROM Permissions
-- DELETE FROM Roles
-- DELETE FROM Users

-- 插入权限数据
IF NOT EXISTS (SELECT 1 FROM Permissions WHERE Code = 'Home.View')
BEGIN
    INSERT INTO [dbo].[Permissions] ([Code], [Name], [Description], [Module], [Action], [Level], [SortOrder], [IsSystem], [RouteUrl])
    VALUES 
    ('Home.View', '首页查看', '查看首页', 'Home', 'View', 2, 1, 1, ''),
    ('Counter.View', '计数器查看', '查看计数器页面', 'Counter', 'View', 2, 2, 1, 'counter'),
    ('Weather.View', '天气预报查看', '查看天气预报页面', 'Weather', 'View', 2, 3, 1, 'weather'),
    ('DeviceScanner.View', '设备扫描查看', '查看设备扫描页面', 'DeviceScanner', 'View', 2, 4, 1, 'devicescanner'),
    ('DeviceRepair.View', '设备报修查看', '查看设备报修页面', 'DeviceRepair', 'View', 2, 5, 1, 'devicerepair'),
    ('CameraTest.View', '摄像头测试查看', '查看摄像头测试页面', 'CameraTest', 'View', 2, 6, 1, 'camera-test'),
    ('UserManagement.View', '用户管理查看', '查看用户管理页面', 'UserManagement', 'View', 2, 7, 1, 'users'),
    ('UserManagement.Create', '用户管理新增', '新增用户', 'UserManagement', 'Create', 3, 8, 1, NULL),
    ('UserManagement.Edit', '用户管理编辑', '编辑用户', 'UserManagement', 'Edit', 3, 9, 1, NULL),
    ('UserManagement.Delete', '用户管理删除', '删除用户', 'UserManagement', 'Delete', 3, 10, 1, NULL),
    ('UserManagement.AssignRoles', '用户角色分配', '为用户分配角色', 'UserManagement', 'AssignRoles', 3, 11, 1, NULL),
    ('UserManagement.AssignDepartment', '用户部门分配', '为用户分配部门', 'UserManagement', 'AssignDepartment', 3, 12, 1, NULL),
    ('RoleManagement.View', '角色管理查看', '查看角色管理页面', 'RoleManagement', 'View', 2, 13, 1, 'roles'),
    ('RoleManagement.Create', '角色管理新增', '新增角色', 'RoleManagement', 'Create', 3, 14, 1, NULL),
    ('RoleManagement.Edit', '角色管理编辑', '编辑角色', 'RoleManagement', 'Edit', 3, 15, 1, NULL),
    ('RoleManagement.Delete', '角色管理删除', '删除角色', 'RoleManagement', 'Delete', 3, 16, 1, NULL),
    ('RoleManagement.AssignPermissions', '角色权限配置', '为角色配置权限', 'RoleManagement', 'AssignPermissions', 3, 17, 1, NULL),
    ('PermissionManagement.View', '权限管理查看', '查看权限管理页面', 'PermissionManagement', 'View', 2, 18, 1, 'permissions'),
    ('PermissionManagement.Create', '权限管理新增', '新增权限', 'PermissionManagement', 'Create', 3, 19, 1, NULL),
    ('PermissionManagement.Edit', '权限管理编辑', '编辑权限', 'PermissionManagement', 'Edit', 3, 20, 1, NULL),
    ('PermissionManagement.Delete', '权限管理删除', '删除权限', 'PermissionManagement', 'Delete', 3, 21, 1, NULL),
    ('MenuManagement.View', '菜单管理查看', '查看菜单管理页面', 'MenuManagement', 'View', 2, 22, 1, 'menu-management'),
    ('MenuManagement.Create', '菜单管理新增', '新增菜单', 'MenuManagement', 'Create', 3, 23, 1, NULL),
    ('MenuManagement.Edit', '菜单管理编辑', '编辑菜单', 'MenuManagement', 'Edit', 3, 24, 1, NULL),
    ('MenuManagement.Delete', '菜单管理删除', '删除菜单', 'MenuManagement', 'Delete', 3, 25, 1, NULL),
    ('System.Admin', '系统管理', '系统管理权限', 'System', 'Admin', 1, 26, 1, NULL),

    -- 设备管理模块权限
    ('EquipmentManagement.View', '设备管理查看', '查看设备管理页面', 'EquipmentManagement', 'View', 2, 27, 1, 'equipment-management'),
    ('EquipmentManagement.Create', '设备管理新增', '新增设备', 'EquipmentManagement', 'Create', 3, 28, 1, NULL),
    ('EquipmentManagement.Edit', '设备管理编辑', '编辑设备', 'EquipmentManagement', 'Edit', 3, 29, 1, NULL),
    ('EquipmentManagement.Delete', '设备管理删除', '删除设备', 'EquipmentManagement', 'Delete', 3, 30, 1, NULL),
    ('DepartmentManagement.View', '部门管理查看', '查看部门管理页面', 'DepartmentManagement', 'View', 2, 31, 1, 'department-management'),
    ('DepartmentManagement.Create', '部门管理新增', '新增部门', 'DepartmentManagement', 'Create', 3, 32, 1, NULL),
    ('DepartmentManagement.Edit', '部门管理编辑', '编辑部门', 'DepartmentManagement', 'Edit', 3, 33, 1, NULL),
    ('DepartmentManagement.Delete', '部门管理删除', '删除部门', 'DepartmentManagement', 'Delete', 3, 34, 1, NULL),
    ('LocationManagement.View', '位置管理查看', '查看位置管理页面', 'LocationManagement', 'View', 2, 35, 1, 'location-management'),
    ('LocationManagement.Create', '位置管理新增', '新增位置', 'LocationManagement', 'Create', 3, 36, 1, NULL),
    ('LocationManagement.Edit', '位置管理编辑', '编辑位置', 'LocationManagement', 'Edit', 3, 37, 1, NULL),
    ('LocationManagement.Delete', '位置管理删除', '删除位置', 'LocationManagement', 'Delete', 3, 38, 1, NULL),
    ('EquipmentModelManagement.View', '设备型号管理查看', '查看设备型号管理页面', 'EquipmentModelManagement', 'View', 2, 39, 1, 'equipment-model-management'),
    ('EquipmentModelManagement.Create', '设备型号管理新增', '新增设备型号', 'EquipmentModelManagement', 'Create', 3, 40, 1, NULL),
    ('EquipmentModelManagement.Edit', '设备型号管理编辑', '编辑设备型号', 'EquipmentModelManagement', 'Edit', 3, 41, 1, NULL),
    ('EquipmentModelManagement.Delete', '设备型号管理删除', '删除设备型号', 'EquipmentModelManagement', 'Delete', 3, 42, 1, NULL),
    ('RepairOrderManagement.View', '报修单管理查看', '查看报修单管理页面', 'RepairOrderManagement', 'View', 2, 43, 1, 'repair-order-management'),
    ('RepairOrderManagement.Create', '报修单管理新增', '新增报修单', 'RepairOrderManagement', 'Create', 3, 44, 1, NULL),
    ('RepairOrderManagement.Edit', '报修单管理编辑', '编辑报修单', 'RepairOrderManagement', 'Edit', 3, 45, 1, NULL),
    ('RepairOrderManagement.Delete', '报修单管理删除', '删除报修单', 'RepairOrderManagement', 'Delete', 3, 46, 1, NULL),
    ('MaintenanceDashboard.View', '维修仪表板查看', '查看维修仪表板页面', 'MaintenanceDashboard', 'View', 2, 47, 1, 'maintenance-dashboard'),
    ('CreateRepairOrder.View', '创建报修单查看', '查看创建报修单页面', 'CreateRepairOrder', 'View', 2, 48, 1, 'create-repair-order'),
    ('EquipmentTestPage.View', '设备测试页面查看', '查看设备测试页面', 'EquipmentTestPage', 'View', 2, 49, 1, 'equipment-test'),

    -- 部门类型管理权限
    ('DepartmentType.Manage', '部门类型管理', '部门类型的增删改查权限', 'DepartmentType', 'Manage', 2, 49, 1, 'department-type-management'),
    ('DepartmentType.View', '部门类型查看', '查看部门类型', 'DepartmentType', 'View', 3, 50, 1, NULL),
    ('DepartmentType.Create', '部门类型新增', '新增部门类型', 'DepartmentType', 'Create', 3, 51, 1, NULL),
    ('DepartmentType.Edit', '部门类型编辑', '编辑部门类型', 'DepartmentType', 'Edit', 3, 52, 1, NULL),
    ('DepartmentType.Delete', '部门类型删除', '删除部门类型', 'DepartmentType', 'Delete', 3, 53, 1, NULL),

    -- 工种管理权限
    ('JobType.Manage', '工种管理', '工种的增删改查权限', 'JobType', 'Manage', 2, 54, 1, 'job-type-management'),
    ('JobType.View', '工种查看', '查看工种', 'JobType', 'View', 3, 55, 1, NULL),
    ('JobType.Create', '工种新增', '新增工种', 'JobType', 'Create', 3, 56, 1, NULL),
    ('JobType.Edit', '工种编辑', '编辑工种', 'JobType', 'Edit', 3, 57, 1, NULL),
    ('JobType.Delete', '工种删除', '删除工种', 'JobType', 'Delete', 3, 58, 1, NULL),
    ('JobType.AssignUsers', '工种用户分配', '为工种分配用户', 'JobType', 'AssignUsers', 3, 59, 1, NULL),

    -- 角色部门分配管理权限
    ('RoleDepartmentAssignment.Manage', '角色部门分配管理', '角色部门分配的增删改查权限', 'RoleDepartmentAssignment', 'Manage', 2, 60, 1, 'role-department-assignment-management'),
    ('RoleDepartmentAssignment.View', '角色部门分配查看', '查看角色部门分配', 'RoleDepartmentAssignment', 'View', 3, 61, 1, NULL),
    ('RoleDepartmentAssignment.Assign', '角色部门分配', '为角色分配部门', 'RoleDepartmentAssignment', 'Assign', 3, 62, 1, NULL),
    ('RoleDepartmentAssignment.Unassign', '角色部门取消分配', '取消角色的部门分配', 'RoleDepartmentAssignment', 'Unassign', 3, 63, 1, NULL),

    -- 维修部门管理权限
    ('MaintenanceDepartment.Manage', '维修部门管理', '维修部门权限关系的增删改查权限', 'MaintenanceDepartment', 'Manage', 2, 64, 1, 'maintenance-department-management'),
    ('MaintenanceDepartment.View', '维修部门权限查看', '查看维修部门权限关系', 'MaintenanceDepartment', 'View', 3, 65, 1, NULL),
    ('MaintenanceDepartment.Assign', '维修部门权限分配', '为维修部门分配目标部门', 'MaintenanceDepartment', 'Assign', 3, 66, 1, NULL),
    ('MaintenanceDepartment.Delete', '维修部门权限删除', '删除维修部门权限关系', 'MaintenanceDepartment', 'Delete', 3, 67, 1, NULL)
END
GO

-- 插入部门类型数据
IF NOT EXISTS (SELECT 1 FROM DepartmentTypes WHERE Code = 'Production')
BEGIN
    INSERT INTO [dbo].[DepartmentTypes] ([Code], [Name], [Description], [SortOrder])
    VALUES
    ('Production', '生产车间', '负责产品生产制造的部门', 1),
    ('Maintenance', '维修部门', '负责设备维修保养的部门', 2),
    ('Management', '管理部门', '负责行政管理的部门', 3),
    ('Support', '支持部门', '提供技术和后勤支持的部门', 4)
END
GO

-- 插入工种类型数据
IF NOT EXISTS (SELECT 1 FROM JobTypes WHERE Code = 'Operator')
BEGIN
    INSERT INTO [dbo].[JobTypes] ([Code], [Name], [Description], [Category], [SortOrder])
    VALUES
    -- 生产工种
    ('Operator', '操作员', '设备操作人员', '生产', 1),
    ('QualityInspector', '质检员', '产品质量检验人员', '生产', 2),
    ('ProductionSupervisor', '生产主管', '生产现场管理人员', '生产', 3),

    -- 维修工种
    ('ElectricalTechnician', '电气维修员', '电气设备维修人员', '维修', 11),
    ('MechanicalTechnician', '机械维修员', '机械设备维修人员', '维修', 12),
    ('HydraulicTechnician', '液压维修员', '液压系统维修人员', '维修', 13),
    ('ControlSystemEngineer', '控制系统工程师', '自动化控制系统维修人员', '维修', 14),
    ('MaintenanceSupervisor', '维修主管', '维修部门管理人员', '维修', 15),

    -- 管理工种
    ('Manager', '经理', '部门经理', '管理', 21),
    ('Supervisor', '主管', '部门主管', '管理', 22),
    ('Administrator', '行政人员', '行政管理人员', '管理', 23),

    -- 支持工种
    ('ITSupport', 'IT支持', '信息技术支持人员', '支持', 31),
    ('SafetyOfficer', '安全员', '安全管理人员', '支持', 32),
    ('Logistics', '后勤人员', '后勤保障人员', '支持', 33)
END
GO

-- 插入角色数据
IF NOT EXISTS (SELECT 1 FROM Roles WHERE Code = 'Administrator')
BEGIN
    INSERT INTO [dbo].[Roles] ([Code], [Name], [Description], [SortOrder], [IsSystem])
    VALUES
    ('Administrator', '系统管理员', '拥有系统全部权限', 1, 1),
    ('Operator', '设备操作员', '设备相关功能权限', 2, 1),
    ('Viewer', '访客用户', '仅查看基础信息', 3, 1)
END
GO

-- 插入用户数据（明文密码，包含部门和工种）
IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'admin')
BEGIN
    DECLARE @ZLBId INT, @WXBId INT, @DLBId INT;
    DECLARE @AdminJobId INT, @ElectricalJobId INT, @MechanicalJobId INT;

    SELECT @ZLBId = Id FROM Departments WHERE Code = 'ZLB';
    SELECT @WXBId = Id FROM Departments WHERE Code = 'WXB';
    SELECT @DLBId = Id FROM Departments WHERE Code = 'DLB';

    SELECT @AdminJobId = Id FROM JobTypes WHERE Code = 'Administrator';
    SELECT @ElectricalJobId = Id FROM JobTypes WHERE Code = 'ElectricalTechnician';
    SELECT @MechanicalJobId = Id FROM JobTypes WHERE Code = 'MechanicalTechnician';

    -- 插入用户（不再包含JobTypeId字段）
    INSERT INTO [dbo].[Users] ([Username], [PasswordHash], [DisplayName], [Email], [DepartmentId], [IsEnabled])
    VALUES
    ('admin', 'admin123', '系统管理员', '<EMAIL>', @ZLBId, 1),
    ('operator', 'op123', '设备操作员', '<EMAIL>', @ZLBId, 1),
    ('viewer', 'view123', '电气维修员', '<EMAIL>', @WXBId, 1)

    -- 通过UserJobTypes表分配工种（新的多对多关系）
    DECLARE @AdminUserId INT, @OperatorUserId INT, @ViewerUserId INT
    DECLARE @OperatorJobId INT

    SELECT @AdminUserId = Id FROM Users WHERE Username = 'admin'
    SELECT @OperatorUserId = Id FROM Users WHERE Username = 'operator'
    SELECT @ViewerUserId = Id FROM Users WHERE Username = 'viewer'
    SELECT @OperatorJobId = Id FROM JobTypes WHERE Code = 'Operator'

    -- 先清空现有的用户工种分配
    DELETE FROM UserJobTypes;

    -- 为用户分配工种
    INSERT INTO [dbo].[UserJobTypes] ([UserId], [JobTypeId], [IsPrimary], [SkillLevel], [AcquiredAt], [IsEnabled], [Remark])
    VALUES
    (@AdminUserId, @AdminJobId, 1, 5, GETDATE(), 1, '系统初始化分配'),
    (@AdminUserId, @ElectricalJobId, 0, 5, GETDATE(), 1, '管理员同时具备电气维修技能'),
    (@AdminUserId, @MechanicalJobId, 0, 5, GETDATE(), 1, '管理员同时具备机械维修技能'),
    (@OperatorUserId, @OperatorJobId, 1, 3, GETDATE(), 1, '系统初始化分配'),
    (@ViewerUserId, @ElectricalJobId, 1, 4, GETDATE(), 1, '系统初始化分配')
END
GO

-- 分配角色权限
IF NOT EXISTS (SELECT 1 FROM RolePermissions)
BEGIN
    -- 管理员 - 全部权限
    INSERT INTO [dbo].[RolePermissions] ([RoleId], [PermissionId])
    SELECT r.Id, p.Id 
    FROM Roles r, Permissions p 
    WHERE r.Code = 'Administrator'

    -- 操作员 - 设备相关权限
    INSERT INTO [dbo].[RolePermissions] ([RoleId], [PermissionId])
    SELECT r.Id, p.Id
    FROM Roles r, Permissions p
    WHERE r.Code = 'Operator'
    AND p.Code IN ('Home.View', 'DeviceScanner.View', 'DeviceRepair.View', 'CameraTest.View',
                   'EquipmentManagement.View', 'EquipmentManagement.Create', 'EquipmentManagement.Edit',
                   'DepartmentManagement.View', 'LocationManagement.View', 'EquipmentModelManagement.View',
                   'RepairOrderManagement.View', 'RepairOrderManagement.Create', 'RepairOrderManagement.Edit',
                   'MaintenanceDashboard.View', 'CreateRepairOrder.View', 'EquipmentTestPage.View')

    -- 访客 - 基础查看权限
    INSERT INTO [dbo].[RolePermissions] ([RoleId], [PermissionId])
    SELECT r.Id, p.Id 
    FROM Roles r, Permissions p 
    WHERE r.Code = 'Viewer' 
    AND p.Code IN ('Home.View', 'Weather.View')
END
GO

-- 分配用户角色
IF NOT EXISTS (SELECT 1 FROM UserRoles)
BEGIN
    INSERT INTO [dbo].[UserRoles] ([UserId], [RoleId])
    SELECT u.Id, r.Id 
    FROM Users u, Roles r 
    WHERE (u.Username = 'admin' AND r.Code = 'Administrator')
    OR (u.Username = 'operator' AND r.Code = 'Operator')
    OR (u.Username = 'viewer' AND r.Code = 'Viewer')
END
GO

-- 插入菜单数据
IF NOT EXISTS (SELECT 1 FROM MenuItems WHERE Code = 'Home')
BEGIN
    -- 插入一级菜单
    INSERT INTO [dbo].[MenuItems] ([Code], [Name], [Description], [RouteUrl], [Icon], [Level], [SortOrder], [PermissionCode], [IsEnabled], [IsPublic], [IsSystem], [MenuType])
    VALUES 
    -- 公开菜单
    ('Home', '首页', '系统首页', '', 'Icons.Material.Filled.Home', 1, 1, NULL, 1, 1, 1, 1),
    
    -- 功能菜单
    ('Counter', '计数器', '计数器页面', 'counter', 'Icons.Material.Filled.Add', 1, 10, 'Counter.View', 1, 0, 1, 1),
    ('DeviceScanner', '设备扫描', '设备扫描页面', 'devicescanner', 'Icons.Material.Filled.QrCodeScanner', 1, 20, 'DeviceScanner.View', 1, 0, 1, 1),
    ('CameraTest', '摄像头测试', '摄像头测试页面', 'camera-test', 'Icons.Material.Filled.Camera', 1, 30, 'CameraTest.View', 1, 0, 1, 1),
    ('Weather', '天气预报', '天气预报页面', 'weather', 'Icons.Material.Filled.WbSunny', 1, 40, 'Weather.View', 1, 0, 1, 1),
    ('DeviceRepair', '设备报修', '设备报修页面', 'devicerepair', 'Icons.Material.Filled.Build', 1, 50, 'DeviceRepair.View', 1, 0, 1, 1),

    -- 设备管理分组
    ('EquipmentManagementGroup', '设备管理', '设备管理分组', NULL, 'Icons.Material.Filled.Devices', 1, 60, NULL, 1, 0, 1, 2),

    -- 系统管理分组
    ('SystemManagement', '系统管理', '系统管理分组', NULL, 'Icons.Material.Filled.Settings', 1, 100, NULL, 1, 0, 1, 2),
    
    -- 注意：已移除测试菜单，包括：
    -- - 角色部门权限管理 (RoleDepartmentPermissionManagement)
    -- - 认证调试 (DebugAuth)
    -- - 用户部门测试 (TestUserDepartment)
    -- - 维修权限调试 (DebugMaintenancePermissions)
    -- 这些菜单仅用于开发测试，生产环境不需要

    -- 获取分组菜单的ID
    DECLARE @EquipmentManagementId INT, @SystemManagementId INT;
    SELECT @EquipmentManagementId = Id FROM MenuItems WHERE Code = 'EquipmentManagementGroup';
    SELECT @SystemManagementId = Id FROM MenuItems WHERE Code = 'SystemManagement';

    -- 插入设备管理子菜单
    INSERT INTO [dbo].[MenuItems] ([Code], [Name], [Description], [RouteUrl], [Icon], [ParentId], [Level], [SortOrder], [PermissionCode], [IsEnabled], [IsPublic], [IsSystem], [MenuType])
    VALUES
    ('LocationManagement', '位置管理', '位置管理页面', 'location-management', 'Icons.Material.Filled.LocationOn', @EquipmentManagementId, 2, 61, 'LocationManagement.View', 1, 0, 1, 1),
    ('EquipmentModelManagement', '设备型号管理', '设备型号管理页面', 'equipment-model-management', 'Icons.Material.Filled.Memory', @EquipmentManagementId, 2, 62, 'EquipmentModelManagement.View', 1, 0, 1, 1),
    ('EquipmentManagement', '设备管理', '设备管理页面', 'equipment-management', 'Icons.Material.Filled.Computer', @EquipmentManagementId, 2, 63, 'EquipmentManagement.View', 1, 0, 1, 1),
    ('RepairOrderManagement', '报修单管理', '报修单管理页面', 'repair-order-management', 'Icons.Material.Filled.Assignment', @EquipmentManagementId, 2, 64, 'RepairOrderManagement.View', 1, 0, 1, 1),
    ('MaintenanceDashboard', '维修仪表板', '维修人员工作仪表板', 'maintenance-dashboard', 'Icons.Material.Filled.Dashboard', @EquipmentManagementId, 2, 65, 'MaintenanceDashboard.View', 1, 0, 1, 1),
    ('CreateRepairOrder', '创建报修单', '创建报修单页面', 'create-repair-order', 'Icons.Material.Filled.Add', @EquipmentManagementId, 2, 66, 'CreateRepairOrder.View', 1, 0, 1, 1),
    ('EquipmentTestPage', '设备测试', '设备测试页面', 'equipment-test', 'Icons.Material.Filled.Speed', @EquipmentManagementId, 2, 67, 'EquipmentTestPage.View', 1, 0, 1, 1);

    -- 插入系统管理子菜单
    INSERT INTO [dbo].[MenuItems] ([Code], [Name], [Description], [RouteUrl], [Icon], [ParentId], [Level], [SortOrder], [PermissionCode], [IsEnabled], [IsPublic], [IsSystem], [MenuType])
    VALUES
    ('UserManagement', '用户管理', '用户管理页面', 'users', 'Icons.Material.Filled.People', @SystemManagementId, 2, 101, 'UserManagement.View', 1, 0, 1, 1),
    ('RoleManagement', '角色管理', '角色管理页面', 'roles', 'Icons.Material.Filled.Security', @SystemManagementId, 2, 102, 'RoleManagement.View', 1, 0, 1, 1),
    ('PermissionManagement', '权限管理', '权限管理页面', 'permissions', 'Icons.Material.Filled.Key', @SystemManagementId, 2, 103, 'PermissionManagement.View', 1, 0, 1, 1),
    ('MenuManagement', '菜单管理', '菜单管理页面', 'menu-management', 'Icons.Material.Filled.Menu', @SystemManagementId, 2, 104, 'MenuManagement.View', 1, 0, 1, 1),
    ('UserPermissions', '权限分配', '权限分配页面', 'user-permissions', 'Icons.Material.Filled.PersonAdd', @SystemManagementId, 2, 106, 'UserManagement.AssignRoles', 1, 0, 1, 1),
    ('DepartmentManagement', '部门管理', '部门管理页面', 'department-management', 'Icons.Material.Filled.Business', @SystemManagementId, 2, 107, 'DepartmentManagement.View', 1, 0, 1, 1),
    ('DepartmentTypeManagement', '部门类型管理', '部门类型维护和管理', 'department-type-management', 'Icons.Material.Filled.Category', @SystemManagementId, 2, 108, 'DepartmentType.Manage', 1, 0, 1, 1),
    ('JobTypeManagement', '工种管理', '工种维护和管理', 'job-type-management', 'Icons.Material.Filled.Work', @SystemManagementId, 2, 109, 'JobType.Manage', 1, 0, 1, 1),
    ('RoleDepartmentAssignmentManagement', '角色权限部门', '角色权限部门管理页面，设置角色可访问的部门数据', 'role-department-assignment-management', 'Icons.Material.Filled.Assignment', @SystemManagementId, 2, 110, 'RoleDepartmentAssignment.Manage', 1, 0, 1, 1),
    ('UserDepartmentAssignmentManagement', '用户所属部门', '用户所属部门管理页面，设置用户在组织架构中的归属', 'user-department-assignment-management', 'Icons.Material.Filled.PersonPin', @SystemManagementId, 2, 111, 'UserManagement.AssignDepartment', 1, 0, 1, 1),
    ('MaintenanceDepartmentManagement', '维修部门管理', '维修部门权限关系管理页面', 'maintenance-department-management', 'Icons.Material.Filled.Engineering', @SystemManagementId, 2, 112, 'MaintenanceDepartment.Manage', 1, 0, 1, 1);
END
GO

-- =============================================
-- 4. 存储过程
-- =============================================

-- 用户验证存储过程（基础版）
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_ValidateUser')
    DROP PROCEDURE [dbo].[sp_ValidateUser]
GO

CREATE PROCEDURE [dbo].[sp_ValidateUser]
    @Username NVARCHAR(50),
    @Password NVARCHAR(255),
    @IsValid BIT OUTPUT,
    @UserId INT OUTPUT,
    @DisplayName NVARCHAR(100) OUTPUT,
    @IsLocked BIT OUTPUT,
    @FailureReason NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 初始化输出参数
    SET @IsValid = 0;
    SET @UserId = NULL;
    SET @DisplayName = NULL;
    SET @IsLocked = 0;
    SET @FailureReason = NULL;
    
    DECLARE @StoredPasswordHash NVARCHAR(255);
    DECLARE @IsEnabled BIT;
    DECLARE @LoginFailureCount INT;
    
    -- 查找用户
    SELECT 
        @UserId = Id,
        @StoredPasswordHash = PasswordHash,
        @DisplayName = DisplayName,
        @IsEnabled = IsEnabled,
        @IsLocked = IsLocked,
        @LoginFailureCount = LoginFailureCount
    FROM Users 
    WHERE Username = @Username;
    
    -- 检查用户是否存在
    IF @UserId IS NULL
    BEGIN
        SET @FailureReason = '用户不存在';
        RETURN;
    END
    
    -- 检查用户是否启用
    IF @IsEnabled = 0
    BEGIN
        SET @FailureReason = '用户已被禁用';
        RETURN;
    END
    
    -- 检查用户是否锁定
    IF @IsLocked = 1
    BEGIN
        SET @FailureReason = '用户账户已被锁定';
        RETURN;
    END
    
    -- 验证密码（明文比较，暂不加密）
    DECLARE @IsPasswordValid BIT = 0;
    
    -- 直接进行明文密码比较
    IF @StoredPasswordHash = @Password
    BEGIN
        SET @IsPasswordValid = 1;
    END
    ELSE
    BEGIN
        SET @IsPasswordValid = 0;
    END
    
    IF @IsPasswordValid = 1
    BEGIN
        -- 密码正确，重置失败次数
        UPDATE Users 
        SET LoginFailureCount = 0,
            LastLoginTime = GETDATE(),
            UpdatedAt = GETDATE()
        WHERE Id = @UserId;
        
        SET @IsValid = 1;
        
        -- 记录成功登录日志
        INSERT INTO LoginLogs (Username, IsSuccess, LoginTime)
        VALUES (@Username, 1, GETDATE());
    END
    ELSE
    BEGIN
        -- 密码错误，增加失败次数
        SET @LoginFailureCount = @LoginFailureCount + 1;
        
        UPDATE Users 
        SET LoginFailureCount = @LoginFailureCount,
            UpdatedAt = GETDATE(),
            IsLocked = CASE WHEN @LoginFailureCount >= 5 THEN 1 ELSE 0 END,
            LockedAt = CASE WHEN @LoginFailureCount >= 5 THEN GETDATE() ELSE LockedAt END,
            LockReason = CASE WHEN @LoginFailureCount >= 5 THEN '连续登录失败' + CAST(@LoginFailureCount AS NVARCHAR(10)) + '次，账户已自动锁定' ELSE LockReason END
        WHERE Id = @UserId;
        
        SET @FailureReason = '密码错误';
        
        -- 记录失败登录日志
        INSERT INTO LoginLogs (Username, IsSuccess, FailureReason, LoginTime)
        VALUES (@Username, 0, @FailureReason, GETDATE());
        
        -- 检查是否需要锁定
        IF @LoginFailureCount >= 5
        BEGIN
            SET @IsLocked = 1;
            SET @FailureReason = '连续登录失败次数过多，账户已被锁定';
        END
    END
END
GO

-- 获取用户信息存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetUserInfo')
    DROP PROCEDURE [dbo].[sp_GetUserInfo]
GO

CREATE PROCEDURE [dbo].[sp_GetUserInfo]
    @UserId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.Id,
        u.Username,
        u.DisplayName,
        u.Email,
        u.Phone,
        u.IsEnabled,
        u.IsLocked,
        u.LoginFailureCount,
        u.LastLoginTime,
        u.LastLoginIp,
        u.CreatedAt,
        u.Remark
    FROM Users u
    WHERE u.Id = @UserId;
END
GO

-- 获取用户权限存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetUserPermissions')
    DROP PROCEDURE [dbo].[sp_GetUserPermissions]
GO

CREATE PROCEDURE [dbo].[sp_GetUserPermissions]
    @UserId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 创建临时表存储权限
    CREATE TABLE #UserPermissions (
        PermissionCode NVARCHAR(100),
        PermissionName NVARCHAR(100),
        Module NVARCHAR(50),
        Action NVARCHAR(50),
        Level INT,
        RouteUrl NVARCHAR(200)
    );
    
    -- 1. 通过角色获取权限
    INSERT INTO #UserPermissions
    SELECT DISTINCT 
        p.Code,
        p.Name,
        p.Module,
        p.Action,
        p.Level,
        p.RouteUrl
    FROM Users u
    INNER JOIN UserRoles ur ON u.Id = ur.UserId
    INNER JOIN Roles r ON ur.RoleId = r.Id
    INNER JOIN RolePermissions rp ON r.Id = rp.RoleId
    INNER JOIN Permissions p ON rp.PermissionId = p.Id
    WHERE u.Id = @UserId 
    AND ur.IsEnabled = 1 
    AND r.IsEnabled = 1 
    AND rp.IsEnabled = 1 
    AND p.IsEnabled = 1
    AND (ur.ExpiresAt IS NULL OR ur.ExpiresAt > GETDATE());
    
    -- 2. 添加用户直接权限（授权）
    INSERT INTO #UserPermissions
    SELECT DISTINCT 
        p.Code,
        p.Name,
        p.Module,
        p.Action,
        p.Level,
        p.RouteUrl
    FROM Users u
    INNER JOIN UserPermissions up ON u.Id = up.UserId
    INNER JOIN Permissions p ON up.PermissionId = p.Id
    WHERE u.Id = @UserId 
    AND up.IsEnabled = 1 
    AND up.IsGranted = 1 
    AND p.IsEnabled = 1
    AND (up.ExpiresAt IS NULL OR up.ExpiresAt > GETDATE())
    AND p.Code NOT IN (SELECT PermissionCode FROM #UserPermissions);
    
    -- 3. 移除用户直接权限（拒绝）
    DELETE FROM #UserPermissions 
    WHERE PermissionCode IN (
        SELECT p.Code
        FROM Users u
        INNER JOIN UserPermissions up ON u.Id = up.UserId
        INNER JOIN Permissions p ON up.PermissionId = p.Id
        WHERE u.Id = @UserId 
        AND up.IsEnabled = 1 
        AND up.IsGranted = 0 
        AND p.IsEnabled = 1
        AND (up.ExpiresAt IS NULL OR up.ExpiresAt > GETDATE())
    );
    
    -- 返回最终权限列表
    SELECT * FROM #UserPermissions
    ORDER BY Module, Level, PermissionCode;
    
    DROP TABLE #UserPermissions;
END
GO

-- 创建用户存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_CreateUser')
    DROP PROCEDURE [dbo].[sp_CreateUser]
GO

CREATE PROCEDURE [dbo].[sp_CreateUser]
    @Username NVARCHAR(50),
    @PasswordHash NVARCHAR(255),  -- 明文密码
    @DisplayName NVARCHAR(100),
    @Email NVARCHAR(100) = NULL,
    @Phone NVARCHAR(20) = NULL,
    @Remark NVARCHAR(500) = NULL,
    @IsSuccess BIT OUTPUT,
    @ErrorMessage NVARCHAR(500) OUTPUT,
    @NewUserId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 初始化输出参数
    SET @IsSuccess = 0;
    SET @ErrorMessage = NULL;
    SET @NewUserId = NULL;
    
    -- 检查用户名是否已存在
    IF EXISTS (SELECT 1 FROM Users WHERE Username = @Username)
    BEGIN
        SET @ErrorMessage = '用户名已存在';
        RETURN;
    END
    
    -- 检查邮箱是否已存在
    IF @Email IS NOT NULL AND EXISTS (SELECT 1 FROM Users WHERE Email = @Email)
    BEGIN
        SET @ErrorMessage = '邮箱已存在';
        RETURN;
    END
    
    BEGIN TRY
        -- 创建用户
        INSERT INTO Users (Username, PasswordHash, DisplayName, Email, Phone, Remark)
        VALUES (@Username, @PasswordHash, @DisplayName, @Email, @Phone, @Remark);
        
        SET @NewUserId = SCOPE_IDENTITY();
        SET @IsSuccess = 1;
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = ERROR_MESSAGE();
    END CATCH
END
GO

-- 分配用户角色存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_AssignUserRole')
    DROP PROCEDURE [dbo].[sp_AssignUserRole]
GO

CREATE PROCEDURE [dbo].[sp_AssignUserRole]
    @UserId INT,
    @RoleId INT,
    @ExpiresAt DATETIME2 = NULL,
    @Remark NVARCHAR(500) = NULL,
    @IsSuccess BIT OUTPUT,
    @ErrorMessage NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 初始化输出参数
    SET @IsSuccess = 0;
    SET @ErrorMessage = NULL;
    
    -- 检查用户是否存在
    IF NOT EXISTS (SELECT 1 FROM Users WHERE Id = @UserId)
    BEGIN
        SET @ErrorMessage = '用户不存在';
        RETURN;
    END
    
    -- 检查角色是否存在
    IF NOT EXISTS (SELECT 1 FROM Roles WHERE Id = @RoleId AND IsEnabled = 1)
    BEGIN
        SET @ErrorMessage = '角色不存在或已禁用';
        RETURN;
    END
    
    -- 检查是否已分配该角色
    IF EXISTS (SELECT 1 FROM UserRoles WHERE UserId = @UserId AND RoleId = @RoleId AND IsEnabled = 1)
    BEGIN
        SET @ErrorMessage = '用户已拥有该角色';
        RETURN;
    END
    
    BEGIN TRY
        -- 分配角色
        INSERT INTO UserRoles (UserId, RoleId, ExpiresAt, Remark)
        VALUES (@UserId, @RoleId, @ExpiresAt, @Remark);
        
        SET @IsSuccess = 1;
    END TRY
    BEGIN CATCH
        SET @ErrorMessage = ERROR_MESSAGE();
    END CATCH
END
GO

-- 获取所有用户存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAllUsers')
    DROP PROCEDURE [dbo].[sp_GetAllUsers]
GO

CREATE PROCEDURE [dbo].[sp_GetAllUsers]
    @PageIndex INT = 1,
    @PageSize INT = 20,
    @SearchKeyword NVARCHAR(100) = NULL,
    @IsEnabled BIT = NULL,
    @IsLocked BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @Offset INT = (@PageIndex - 1) * @PageSize;
    
    -- 构建查询条件
    DECLARE @SQL NVARCHAR(MAX) = '
    SELECT 
        u.Id,
        u.Username,
        u.DisplayName,
        u.Email,
        u.Phone,
        u.IsEnabled,
        u.IsLocked,
        u.LoginFailureCount,
        u.LastLoginTime,
        u.CreatedAt,
        u.Remark,
        COUNT(*) OVER() AS TotalCount
    FROM Users u
    WHERE 1=1';
    
    -- 添加搜索条件
    IF @SearchKeyword IS NOT NULL
        SET @SQL = @SQL + ' AND (u.Username LIKE ''%' + @SearchKeyword + '%'' OR u.DisplayName LIKE ''%' + @SearchKeyword + '%'' OR u.Email LIKE ''%' + @SearchKeyword + '%'')';
    
    IF @IsEnabled IS NOT NULL
        SET @SQL = @SQL + ' AND u.IsEnabled = ' + CAST(@IsEnabled AS NVARCHAR(1));
    
    IF @IsLocked IS NOT NULL
        SET @SQL = @SQL + ' AND u.IsLocked = ' + CAST(@IsLocked AS NVARCHAR(1));
    
    -- 添加排序和分页
    SET @SQL = @SQL + ' ORDER BY u.CreatedAt DESC OFFSET ' + CAST(@Offset AS NVARCHAR(10)) + ' ROWS FETCH NEXT ' + CAST(@PageSize AS NVARCHAR(10)) + ' ROWS ONLY';
    
    EXEC sp_executesql @SQL;
END
GO

-- 系统统计存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_PermissionSystemStats')
    DROP PROCEDURE [dbo].[sp_PermissionSystemStats]
GO

CREATE PROCEDURE [dbo].[sp_PermissionSystemStats]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        'Users' AS TableName,
        COUNT(*) AS TotalCount,
        SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount,
        SUM(CASE WHEN IsLocked = 1 THEN 1 ELSE 0 END) AS LockedCount
    FROM Users
    
    UNION ALL
    
    SELECT 
        'Roles' AS TableName,
        COUNT(*) AS TotalCount,
        SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount,
        0 AS LockedCount
    FROM Roles
    
    UNION ALL
    
    SELECT 
        'Permissions' AS TableName,
        COUNT(*) AS TotalCount,
        SUM(CASE WHEN IsEnabled = 1 THEN 1 ELSE 0 END) AS EnabledCount,
        0 AS LockedCount
    FROM Permissions
    
    UNION ALL
    
    SELECT 
        'LoginLogs' AS TableName,
        COUNT(*) AS TotalCount,
        SUM(CASE WHEN IsSuccess = 1 THEN 1 ELSE 0 END) AS EnabledCount,
        SUM(CASE WHEN IsSuccess = 0 THEN 1 ELSE 0 END) AS LockedCount
    FROM LoginLogs
    WHERE LoginTime >= DATEADD(DAY, -30, GETDATE());
END
GO

-- 系统维护存储过程
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_MaintenancePermissionSystem')
    DROP PROCEDURE [dbo].[sp_MaintenancePermissionSystem]
GO

CREATE PROCEDURE [dbo].[sp_MaintenancePermissionSystem]
    @Action NVARCHAR(50) -- 'CLEANUP_LOGS', 'UNLOCK_USERS', 'RESET_FAILURES'
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @Action = 'CLEANUP_LOGS'
    BEGIN
        -- 清理30天前的登录日志
        DELETE FROM LoginLogs 
        WHERE LoginTime < DATEADD(DAY, -30, GETDATE());
        
        SELECT '登录日志清理完成' AS Message, @@ROWCOUNT AS AffectedRows;
    END
    ELSE IF @Action = 'UNLOCK_USERS'
    BEGIN
        -- 解锁所有用户
        UPDATE Users 
        SET IsLocked = 0, 
            LockedAt = NULL, 
            LockReason = NULL,
            UpdatedAt = GETDATE()
        WHERE IsLocked = 1;
        
        SELECT '用户解锁完成' AS Message, @@ROWCOUNT AS AffectedRows;
    END
    ELSE IF @Action = 'RESET_FAILURES'
    BEGIN
        -- 重置所有用户的登录失败次数
        UPDATE Users 
        SET LoginFailureCount = 0,
            UpdatedAt = GETDATE()
        WHERE LoginFailureCount > 0;
        
        SELECT '登录失败次数重置完成' AS Message, @@ROWCOUNT AS AffectedRows;
    END
    ELSE
    BEGIN
        SELECT '无效的操作类型' AS Message, 0 AS AffectedRows;
    END
END
GO

-- =============================================
-- 5. 测试查询
-- =============================================

-- 查看所有用户
SELECT * FROM Users;

-- 查看所有角色
SELECT * FROM Roles;

-- 查看所有权限
SELECT * FROM Permissions ORDER BY Module, SortOrder;

-- 查看用户角色分配
SELECT 
    u.Username,
    u.DisplayName,
    r.Name AS RoleName,
    ur.AssignedAt,
    ur.ExpiresAt
FROM Users u
INNER JOIN UserRoles ur ON u.Id = ur.UserId
INNER JOIN Roles r ON ur.RoleId = r.Id
WHERE ur.IsEnabled = 1
ORDER BY u.Username;

-- 测试用户验证
DECLARE @IsValid BIT, @UserId INT, @DisplayName NVARCHAR(100), @IsLocked BIT, @FailureReason NVARCHAR(500);
EXEC sp_ValidateUser 'admin', 'admin123', @IsValid OUTPUT, @UserId OUTPUT, @DisplayName OUTPUT, @IsLocked OUTPUT, @FailureReason OUTPUT;
SELECT @IsValid AS IsValid, @UserId AS UserId, @DisplayName AS DisplayName, @IsLocked AS IsLocked, @FailureReason AS FailureReason;

-- 测试获取用户权限
EXEC sp_GetUserPermissions 1;

-- 查看系统统计
EXEC sp_PermissionSystemStats;

-- =============================================
-- 4. 插入设备管理基础数据
-- =============================================

-- 插入部门数据（需要先获取部门类型ID）
IF NOT EXISTS (SELECT 1 FROM Departments WHERE Code = 'ZLB')
BEGIN
    DECLARE @ProductionTypeId INT, @MaintenanceTypeId INT, @SupportTypeId INT;
    SELECT @ProductionTypeId = Id FROM DepartmentTypes WHERE Code = 'Production';
    SELECT @MaintenanceTypeId = Id FROM DepartmentTypes WHERE Code = 'Maintenance';
    SELECT @SupportTypeId = Id FROM DepartmentTypes WHERE Code = 'Support';

    INSERT INTO [dbo].[Departments] ([Code], [Name], [Description], [DepartmentTypeId], [ParentId], [Level], [SortOrder], [IsEnabled], [CreatedAt])
    VALUES
        ('ZLB', N'整理部', N'负责织物整理加工的生产车间', @ProductionTypeId, NULL, 1, 1, 1, GETDATE()),
        ('WXB', N'工程部', N'负责设备维修保养的维修部门', @MaintenanceTypeId, NULL, 1, 2, 1, GETDATE()),
        ('DLB', N'动力部', N'负责动力设备管理的维修部门', @MaintenanceTypeId, NULL, 1, 3, 1, GETDATE()),
        ('AQB', N'安全部', N'负责安全管理的支持部门', @SupportTypeId, NULL, 1, 4, 1, GETDATE())
END
GO

-- 为现有用户分配部门
UPDATE Users SET DepartmentId = (SELECT Id FROM Departments WHERE Code = 'ZLB') WHERE Username = 'admin'
UPDATE Users SET DepartmentId = (SELECT Id FROM Departments WHERE Code = 'ZLB') WHERE Username = 'operator'
UPDATE Users SET DepartmentId = (SELECT Id FROM Departments WHERE Code = 'WXB') WHERE Username = 'viewer'
GO

-- 插入角色部门分配数据
IF NOT EXISTS (SELECT 1 FROM RoleDepartmentAssignments)
BEGIN
    -- 管理员角色：分配所有部门
    INSERT INTO [dbo].[RoleDepartmentAssignments] ([RoleId], [DepartmentId], [IsEnabled], [Remark])
    SELECT r.Id, d.Id, 1, N'管理员默认权限'
    FROM Roles r
    CROSS JOIN Departments d
    WHERE r.Code = 'Administrator' AND d.IsEnabled = 1

    -- 设备管理员角色：分配所有部门
    INSERT INTO [dbo].[RoleDepartmentAssignments] ([RoleId], [DepartmentId], [IsEnabled], [Remark])
    SELECT r.Id, d.Id, 1, N'设备管理员默认权限'
    FROM Roles r
    CROSS JOIN Departments d
    WHERE r.Code = 'EquipmentManager' AND d.IsEnabled = 1

    -- 操作员角色：只分配整理部
    INSERT INTO [dbo].[RoleDepartmentAssignments] ([RoleId], [DepartmentId], [IsEnabled], [Remark])
    SELECT r.Id, d.Id, 1, N'操作员默认部门'
    FROM Roles r, Departments d
    WHERE r.Code = 'Operator' AND d.Code = 'ZLB'

    -- 查看者角色：只分配维修部
    INSERT INTO [dbo].[RoleDepartmentAssignments] ([RoleId], [DepartmentId], [IsEnabled], [Remark])
    SELECT r.Id, d.Id, 1, N'查看者默认部门'
    FROM Roles r, Departments d
    WHERE r.Code = 'Viewer' AND d.Code = 'WXB'

    -- 为其他角色分配默认部门（基于用户所属部门）
    INSERT INTO [dbo].[RoleDepartmentAssignments] ([RoleId], [DepartmentId], [IsEnabled], [Remark])
    SELECT DISTINCT r.Id, u.DepartmentId, 1, N'基于用户部门的默认分配'
    FROM Roles r
    INNER JOIN UserRoles ur ON ur.RoleId = r.Id
    INNER JOIN Users u ON u.Id = ur.UserId
    WHERE u.DepartmentId IS NOT NULL
    AND r.Code NOT IN ('Administrator', 'EquipmentManager', 'Operator', 'Viewer')
    AND u.IsEnabled = 1
    AND ur.IsEnabled = 1
    AND NOT EXISTS (
        SELECT 1 FROM RoleDepartmentAssignments rda
        WHERE rda.RoleId = r.Id AND rda.DepartmentId = u.DepartmentId
    )

    PRINT '插入角色部门分配数据成功'
END
GO

-- MaintenancePersonnel 表已移除，维修人员直接从用户工种关系中获取

-- 插入维修部门权限数据
IF NOT EXISTS (SELECT 1 FROM MaintenanceDepartmentPermissions)
BEGIN
    -- 工程部可以维修整理部和织造部的设备
    INSERT INTO [dbo].[MaintenanceDepartmentPermissions] ([MaintenanceDepartmentId], [TargetDepartmentId], [IsEnabled], [Remark])
    SELECT md.Id, td.Id, 1, N'工程部负责整理部设备维修'
    FROM Departments md, Departments td, DepartmentTypes mdt, DepartmentTypes tdt
    WHERE md.Code = 'GCB' AND md.DepartmentTypeId = mdt.Id AND mdt.Code = 'Maintenance'
    AND td.Code = 'ZLB' AND td.DepartmentTypeId = tdt.Id AND tdt.Code = 'Production'

    UNION ALL

    SELECT md.Id, td.Id, 1, N'工程部负责织造部设备维修'
    FROM Departments md, Departments td, DepartmentTypes mdt, DepartmentTypes tdt
    WHERE md.Code = 'GCB' AND md.DepartmentTypeId = mdt.Id AND mdt.Code = 'Maintenance'
    AND td.Code = 'ZZB' AND td.DepartmentTypeId = tdt.Id AND tdt.Code = 'Production'

    -- 整理机修部只能维修整理部的设备
    INSERT INTO [dbo].[MaintenanceDepartmentPermissions] ([MaintenanceDepartmentId], [TargetDepartmentId], [IsEnabled], [Remark])
    SELECT md.Id, td.Id, 1, N'整理机修部专门负责整理部设备维修'
    FROM Departments md, Departments td, DepartmentTypes mdt, DepartmentTypes tdt
    WHERE md.Code = 'ZLJXB' AND md.DepartmentTypeId = mdt.Id AND mdt.Code = 'Maintenance'
    AND td.Code = 'ZLB' AND td.DepartmentTypeId = tdt.Id AND tdt.Code = 'Production'

    PRINT '插入维修部门权限数据成功'
END
GO

-- 插入设备型号数据
IF NOT EXISTS (SELECT 1 FROM EquipmentModels WHERE Code = 'DXJ')
BEGIN
    INSERT INTO [dbo].[EquipmentModels] ([Code], [Name], [Category], [Brand], [Model], [Specifications], [Description], [IsEnabled], [CreatedAt])
    VALUES
        ('DXJ', N'定型机', N'定型机', NULL, NULL, NULL, N'用于织物定型的设备', 1, GETDATE()),
        ('SMJ', N'烧毛机', N'烧毛机', NULL, NULL, NULL, N'用于织物烧毛处理的设备', 1, GETDATE()),
        ('TJJ', N'退浆机', N'退浆机', NULL, NULL, NULL, N'用于织物退浆处理的设备', 1, GETDATE()),
        ('SGJ', N'丝光机', N'丝光机', NULL, NULL, NULL, N'用于织物丝光处理的设备', 1, GETDATE()),
        ('QLJ', N'气流机', N'气流机', NULL, NULL, NULL, N'用于织物气流处理的设备', 1, GETDATE()),
        ('TSJ', N'脱水机', N'脱水机', NULL, NULL, NULL, N'用于织物脱水的设备', 1, GETDATE()),
        ('SXJ', N'水洗机', N'水洗机', NULL, NULL, NULL, N'用于织物水洗的设备', 1, GETDATE()),
        ('PHJ', N'焙烘机', N'焙烘机', NULL, NULL, NULL, N'用于织物焙烘的设备', 1, GETDATE()),
        ('ZHJ', N'单烘桶轧烘机', N'轧烘机', NULL, NULL, NULL, N'用于织物轧烘的设备', 1, GETDATE()),
        ('QMJ', N'起毛机', N'起毛机', NULL, NULL, NULL, N'用于织物起毛的设备', 1, GETDATE()),
        ('ZGJ', N'轧光机', N'轧光机', NULL, NULL, NULL, N'用于织物轧光的设备', 1, GETDATE()),
        ('YSJ', N'预缩机', N'预缩机', NULL, NULL, NULL, N'用于织物预缩的设备', 1, GETDATE()),
        ('MMJ', N'磨毛机', N'磨毛机', NULL, NULL, NULL, N'用于织物磨毛的设备', 1, GETDATE()),
        ('GZJ', N'罐蒸机', N'罐蒸机', NULL, NULL, NULL, N'用于织物罐蒸的设备', 1, GETDATE()),
        ('XYJ', N'洗衣机', N'洗衣机', NULL, NULL, NULL, N'用于织物洗涤的设备', 1, GETDATE()),
        ('KFJ', N'开幅机', N'开幅机', NULL, NULL, NULL, N'用于织物开幅的设备', 1, GETDATE()),
        ('CCJ', N'除尘机组', N'除尘机组', NULL, NULL, NULL, N'用于除尘的设备', 1, GETDATE()),
        ('JMJ', N'剪毛机', N'剪毛机', NULL, NULL, NULL, N'用于织物剪毛的设备', 1, GETDATE()),
        ('ZNJ', N'蒸呢机', N'蒸呢机', NULL, NULL, NULL, N'用于织物蒸呢的设备', 1, GETDATE()),
        ('TJJ2', N'退卷机', N'退卷机', NULL, NULL, NULL, N'用于织物退卷的设备', 1, GETDATE()),
        ('FCJ', N'静电式油烟废气处理机', N'废气处理机', NULL, NULL, NULL, N'用于废气处理的设备', 1, GETDATE()),
        ('DDJ', N'打样定型', N'定型机', NULL, NULL, NULL, N'用于打样定型的设备', 1, GETDATE()),
        ('ZJJ', N'助剂自动配送机', N'配送机', NULL, NULL, NULL, N'用于助剂自动配送的设备', 1, GETDATE()),
        ('HXJ', N'烘箱', N'烘箱', NULL, NULL, NULL, N'用于烘干的设备', 1, GETDATE()),
        ('XZC', N'小轧车', N'轧车', NULL, NULL, NULL, N'用于小批量轧制的设备', 1, GETDATE()),
        ('KSQ', N'开水器', N'开水器', NULL, NULL, NULL, N'用于提供开水的设备', 1, GETDATE())
END
GO



-- 插入位置数据
IF NOT EXISTS (SELECT 1 FROM Locations WHERE Code = 'HZCJ')
BEGIN
    DECLARE @ZLBDeptId INT;
    SELECT @ZLBDeptId = Id FROM Departments WHERE Code = 'ZLB';

    INSERT INTO [dbo].[Locations] ([Code], [Name], [DepartmentId], [ParentId], [Level], [Address], [Description], [IsEnabled], [CreatedAt])
    VALUES
        ('HZCJ', N'后整车间', @ZLBDeptId, NULL, 1, NULL, N'后整理主车间', 1, GETDATE()),
        ('QMCJ', N'起毛车间', @ZLBDeptId, NULL, 1, NULL, N'起毛专用车间', 1, GETDATE()),
        ('HZL', N'后整理', @ZLBDeptId, NULL, 1, NULL, N'后整理区域', 1, GETDATE()),
        ('HZTHCJ', N'后整天虹车间', @ZLBDeptId, NULL, 1, NULL, N'后整天虹车间', 1, GETDATE()),
        ('HZHYS', N'后整化验室', @ZLBDeptId, NULL, 1, NULL, N'后整化验室', 1, GETDATE()),
        ('HZHLF', N'后整化料房', @ZLBDeptId, NULL, 1, NULL, N'后整化料房', 1, GETDATE()),
        ('HYS', N'化验室', @ZLBDeptId, NULL, 1, NULL, N'化验室', 1, GETDATE());

    -- 获取父位置ID
    DECLARE @QMCJId INT;
    SELECT @QMCJId = Id FROM Locations WHERE Code = 'QMCJ';

    INSERT INTO [dbo].[Locations] ([Code], [Name], [DepartmentId], [ParentId], [Level], [Address], [Description], [IsEnabled], [CreatedAt])
    VALUES
        ('QM1HJ', N'起毛1号机', @ZLBDeptId, @QMCJId, 2, NULL, N'起毛1号机位置', 1, GETDATE()),
        ('QM2HJ', N'起毛2号机', @ZLBDeptId, @QMCJId, 2, NULL, N'起毛2号机位置', 1, GETDATE())
END
GO

-- 插入设备数据（第一批：定型机、烧毛机、退浆机、丝光机）
IF NOT EXISTS (SELECT 1 FROM Equipment WHERE Code = 'FN-DX-001')
BEGIN
    DECLARE @ZLBDeptId INT, @HZCJLocationId INT;
    DECLARE @DXJModelId INT, @SMJModelId INT, @TJJModelId INT, @SGJModelId INT;

    SELECT @ZLBDeptId = Id FROM Departments WHERE Code = 'ZLB';
    SELECT @HZCJLocationId = Id FROM Locations WHERE Code = 'HZCJ';
    SELECT @DXJModelId = Id FROM EquipmentModels WHERE Code = 'DXJ';
    SELECT @SMJModelId = Id FROM EquipmentModels WHERE Code = 'SMJ';
    SELECT @TJJModelId = Id FROM EquipmentModels WHERE Code = 'TJJ';
    SELECT @SGJModelId = Id FROM EquipmentModels WHERE Code = 'SGJ';

    -- 验证必要的ID是否存在
    IF @ZLBDeptId IS NULL OR @HZCJLocationId IS NULL OR @DXJModelId IS NULL OR @SMJModelId IS NULL OR @TJJModelId IS NULL OR @SGJModelId IS NULL
    BEGIN
        RAISERROR('第一批设备数据插入失败：缺少必要的关联数据', 16, 1);
        RETURN;
    END

    INSERT INTO [dbo].[Equipment] ([Code], [Name], [DepartmentId], [ModelId], [LocationId], [Status], [IsEnabled], [CreatedAt])
    VALUES
        -- 定型机设备 (1-6号)
        ('FN-DX-001', N'定型1号机', @ZLBDeptId, @DXJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-DX-002', N'定型2号机', @ZLBDeptId, @DXJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-DX-003', N'定型3号机', @ZLBDeptId, @DXJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-DX-004', N'定型4号机', @ZLBDeptId, @DXJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-DX-005', N'定型5号机', @ZLBDeptId, @DXJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-DX-006', N'定型6号机', @ZLBDeptId, @DXJModelId, @HZCJLocationId, 1, 1, GETDATE()),

        -- 烧毛机设备 (1-2号)
        ('FN-SM-001', N'烧毛1号机', @ZLBDeptId, @SMJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-SM-002', N'烧毛2号机', @ZLBDeptId, @SMJModelId, @HZCJLocationId, 1, 1, GETDATE()),

        -- 退浆机设备 (1-2号)
        ('FN-TJ-001', N'退浆1号机', @ZLBDeptId, @TJJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-TJ-002', N'退浆2号机', @ZLBDeptId, @TJJModelId, @HZCJLocationId, 1, 1, GETDATE()),

        -- 丝光机设备 (1-2号)
        ('FN-SG-001', N'丝光1号机', @ZLBDeptId, @SGJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-SG-002', N'丝光2号机', @ZLBDeptId, @SGJModelId, @HZCJLocationId, 1, 1, GETDATE())
END
GO

-- 插入设备数据（第二批：气流机、脱水机、水洗机、焙烘机、轧烘机）
IF NOT EXISTS (SELECT 1 FROM Equipment WHERE Code = 'FN-QL-001')
BEGIN
    DECLARE @ZLBDeptId INT, @HZCJLocationId INT;
    DECLARE @QLJModelId INT, @TSJModelId INT, @SXJModelId INT, @PHJModelId INT, @ZHJModelId INT;

    SELECT @ZLBDeptId = Id FROM Departments WHERE Code = 'ZLB';
    SELECT @HZCJLocationId = Id FROM Locations WHERE Code = 'HZCJ';
    SELECT @QLJModelId = Id FROM EquipmentModels WHERE Code = 'QLJ';
    SELECT @TSJModelId = Id FROM EquipmentModels WHERE Code = 'TSJ';
    SELECT @SXJModelId = Id FROM EquipmentModels WHERE Code = 'SXJ';
    SELECT @PHJModelId = Id FROM EquipmentModels WHERE Code = 'PHJ';
    SELECT @ZHJModelId = Id FROM EquipmentModels WHERE Code = 'ZHJ';

    INSERT INTO [dbo].[Equipment] ([Code], [Name], [DepartmentId], [ModelId], [LocationId], [Status], [IsEnabled], [CreatedAt])
    VALUES
        -- 气流机设备 (1-3号)
        ('FN-QL-001', N'气流1号机', @ZLBDeptId, @QLJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-QL-002', N'气流2号机', @ZLBDeptId, @QLJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-QL-003', N'气流3号机', @ZLBDeptId, @QLJModelId, @HZCJLocationId, 1, 1, GETDATE()),

        -- 脱水机设备
        ('FN-TS-001', N'脱水1号机', @ZLBDeptId, @TSJModelId, @HZCJLocationId, 1, 1, GETDATE()),

        -- 水洗机设备 (1-3号)
        ('FN-SX-001', N'水洗机', @ZLBDeptId, @SXJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-SX-002', N'松式水洗1号机', @ZLBDeptId, @SXJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-SX-003', N'松式水洗2号机', @ZLBDeptId, @SXJModelId, @HZCJLocationId, 1, 1, GETDATE()),

        -- 焙烘机设备
        ('FN-PH-001', N'焙烘1号机', @ZLBDeptId, @PHJModelId, @HZCJLocationId, 1, 1, GETDATE()),

        -- 单烘桶轧烘机设备 (1-2号)
        ('FN-ZH-001', N'单烘桶轧烘1号机', @ZLBDeptId, @ZHJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-ZH-002', N'单烘桶轧烘2号机', @ZLBDeptId, @ZHJModelId, @HZCJLocationId, 1, 1, GETDATE())
END
GO

-- 插入设备数据（第三批：起毛机设备 3-18号）
IF NOT EXISTS (SELECT 1 FROM Equipment WHERE Code = 'FN-QM-003')
BEGIN
    DECLARE @ZLBDeptId INT, @QMCJLocationId INT, @QMJModelId INT;

    SELECT @ZLBDeptId = Id FROM Departments WHERE Code = 'ZLB';
    SELECT @QMCJLocationId = Id FROM Locations WHERE Code = 'QMCJ';
    SELECT @QMJModelId = Id FROM EquipmentModels WHERE Code = 'QMJ';

    INSERT INTO [dbo].[Equipment] ([Code], [Name], [DepartmentId], [ModelId], [LocationId], [Status], [IsEnabled], [CreatedAt])
    VALUES
        -- 起毛机设备 (3-18号)
        ('FN-QM-003', N'高效起毛3号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-004', N'高效起毛4号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-005', N'高效起毛5号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-006', N'高效起毛6号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-007', N'高效起毛7号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-008', N'高效起毛8号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-009', N'汉斯起毛机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-010', N'强力起毛9号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-011', N'强力起毛10号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-012', N'强力起毛11号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-013', N'强力起毛12号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-014', N'强力起毛13号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-015', N'强力起毛14号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-016', N'强力起毛15号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-017', N'强力起毛16号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-018', N'强力起毛17号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-QM-019', N'强力起毛18号机', @ZLBDeptId, @QMJModelId, @QMCJLocationId, 1, 1, GETDATE())
END
GO

-- 插入设备数据（第四批：其他设备）
IF NOT EXISTS (SELECT 1 FROM Equipment WHERE Code = 'FN-ZG-001')
BEGIN
    DECLARE @ZLBDeptId INT;
    DECLARE @HZCJLocationId INT, @QMCJLocationId INT, @HZLLocationId INT, @HZTHCJLocationId INT;
    DECLARE @ZGJModelId INT, @YSJModelId INT, @MMJModelId INT, @GZJModelId INT, @XYJModelId INT, @KFJModelId INT;
    DECLARE @CCJModelId INT, @JMJModelId INT, @ZNJModelId INT, @TJJ2ModelId INT;

    SELECT @ZLBDeptId = Id FROM Departments WHERE Code = 'ZLB';
    SELECT @HZCJLocationId = Id FROM Locations WHERE Code = 'HZCJ';
    SELECT @QMCJLocationId = Id FROM Locations WHERE Code = 'QMCJ';
    SELECT @HZLLocationId = Id FROM Locations WHERE Code = 'HZL';
    SELECT @HZTHCJLocationId = Id FROM Locations WHERE Code = 'HZTHCJ';

    SELECT @ZGJModelId = Id FROM EquipmentModels WHERE Code = 'ZGJ';
    SELECT @YSJModelId = Id FROM EquipmentModels WHERE Code = 'YSJ';
    SELECT @MMJModelId = Id FROM EquipmentModels WHERE Code = 'MMJ';
    SELECT @GZJModelId = Id FROM EquipmentModels WHERE Code = 'GZJ';
    SELECT @XYJModelId = Id FROM EquipmentModels WHERE Code = 'XYJ';
    SELECT @KFJModelId = Id FROM EquipmentModels WHERE Code = 'KFJ';
    SELECT @CCJModelId = Id FROM EquipmentModels WHERE Code = 'CCJ';
    SELECT @JMJModelId = Id FROM EquipmentModels WHERE Code = 'JMJ';
    SELECT @ZNJModelId = Id FROM EquipmentModels WHERE Code = 'ZNJ';
    SELECT @TJJ2ModelId = Id FROM EquipmentModels WHERE Code = 'TJJ2';

    INSERT INTO [dbo].[Equipment] ([Code], [Name], [DepartmentId], [ModelId], [LocationId], [Status], [IsEnabled], [CreatedAt])
    VALUES
        -- 轧光机、预缩机、磨毛机、罐蒸机、洗衣机、开幅机
        ('FN-ZG-001', N'轧光1号机', @ZLBDeptId, @ZGJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-YS-001', N'预缩1号机', @ZLBDeptId, @YSJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-YS-002', N'预缩2号机', @ZLBDeptId, @YSJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-YS-003', N'预缩3号机', @ZLBDeptId, @YSJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-YS-004', N'预缩4号机', @ZLBDeptId, @YSJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-MM-001', N'磨毛1号机', @ZLBDeptId, @MMJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-MM-002', N'磨毛2号机', @ZLBDeptId, @MMJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-GZ-001', N'罐蒸1号机', @ZLBDeptId, @GZJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-XY-001', N'1#洗衣机', @ZLBDeptId, @XYJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-KF-001', N'退捻开幅机', @ZLBDeptId, @KFJModelId, @HZCJLocationId, 1, 1, GETDATE()),

        -- 除尘机组
        ('FN-CC-001', N'除尘机组1号', @ZLBDeptId, @CCJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-CC-002', N'除尘机组2号', @ZLBDeptId, @CCJModelId, @QMCJLocationId, 1, 1, GETDATE()),

        -- 剪毛机、蒸呢机、退卷机
        ('FN-JM-001', N'汉斯剪毛机', @ZLBDeptId, @JMJModelId, @HZCJLocationId, 1, 1, GETDATE()),
        ('FN-JM-002', N'拉法剪毛机', @ZLBDeptId, @JMJModelId, @QMCJLocationId, 1, 1, GETDATE()),
        ('FN-ZN-001', N'1#蒸呢机', @ZLBDeptId, @ZNJModelId, @HZLLocationId, 1, 1, GETDATE()),
        ('FN-TJ-003', N'框架式退卷1号机', @ZLBDeptId, @TJJ2ModelId, @HZTHCJLocationId, 1, 1, GETDATE()),
        ('FN-TJ-004', N'框架式退卷2号机', @ZLBDeptId, @TJJ2ModelId, @HZTHCJLocationId, 1, 1, GETDATE())
END
GO

-- 插入设备数据（第五批：最后一批设备）
IF NOT EXISTS (SELECT 1 FROM Equipment WHERE Code = 'FN-FC-001')
BEGIN
    DECLARE @ZLBDeptId INT;
    DECLARE @HZLLocationId INT, @HZHYSLocationId INT, @HZHLFLocationId INT, @HYSLocationId INT;
    DECLARE @FCJModelId INT, @DDJModelId INT, @ZJJModelId INT, @HXJModelId INT, @XZCModelId INT, @KSQModelId INT;

    SELECT @ZLBDeptId = Id FROM Departments WHERE Code = 'ZLB';
    SELECT @HZLLocationId = Id FROM Locations WHERE Code = 'HZL';
    SELECT @HZHYSLocationId = Id FROM Locations WHERE Code = 'HZHYS';
    SELECT @HZHLFLocationId = Id FROM Locations WHERE Code = 'HZHLF';
    SELECT @HYSLocationId = Id FROM Locations WHERE Code = 'HYS';

    SELECT @FCJModelId = Id FROM EquipmentModels WHERE Code = 'FCJ';
    SELECT @DDJModelId = Id FROM EquipmentModels WHERE Code = 'DDJ';
    SELECT @ZJJModelId = Id FROM EquipmentModels WHERE Code = 'ZJJ';
    SELECT @HXJModelId = Id FROM EquipmentModels WHERE Code = 'HXJ';
    SELECT @XZCModelId = Id FROM EquipmentModels WHERE Code = 'XZC';
    SELECT @KSQModelId = Id FROM EquipmentModels WHERE Code = 'KSQ';

    -- 验证必要的ID是否存在
    IF @ZLBDeptId IS NULL OR @HZLLocationId IS NULL OR @HZHYSLocationId IS NULL OR @HZHLFLocationId IS NULL OR @HYSLocationId IS NULL
        OR @FCJModelId IS NULL OR @DDJModelId IS NULL OR @ZJJModelId IS NULL OR @HXJModelId IS NULL OR @XZCModelId IS NULL OR @KSQModelId IS NULL
    BEGIN
        RAISERROR('第五批设备数据插入失败：缺少必要的关联数据', 16, 1);
        RETURN;
    END

    INSERT INTO [dbo].[Equipment] ([Code], [Name], [DepartmentId], [ModelId], [LocationId], [Status], [IsEnabled], [CreatedAt])
    VALUES
        -- 废气处理机、打样定型机、助剂配送机、烘箱、小轧车、开水器
        ('FN-FC-001', N'静电式油烟废气处理机', @ZLBDeptId, @FCJModelId, @HZLLocationId, 1, 1, GETDATE()),
        ('FN-DD-001', N'1#打样定型机', @ZLBDeptId, @DDJModelId, @HZHYSLocationId, 1, 1, GETDATE()),
        ('FN-ZJ-001', N'1号助剂自动配送机', @ZLBDeptId, @ZJJModelId, @HZHLFLocationId, 1, 1, GETDATE()),
        ('FN-ZJ-002', N'2号助剂自动配送机', @ZLBDeptId, @ZJJModelId, @HZHLFLocationId, 1, 1, GETDATE()),
        ('FN-ZJ-003', N'3号助剂自动配送机', @ZLBDeptId, @ZJJModelId, @HZHLFLocationId, 1, 1, GETDATE()),
        ('FN-HX-001', N'烘箱1号', @ZLBDeptId, @HXJModelId, @HZHYSLocationId, 1, 1, GETDATE()),
        ('FN-HX-002', N'恒温干烘箱1号', @ZLBDeptId, @HXJModelId, @HZHYSLocationId, 1, 1, GETDATE()),
        ('FN-YC-001', N'小轧车1号', @ZLBDeptId, @XZCModelId, @HZHYSLocationId, 1, 1, GETDATE()),
        ('FN-KS-001', N'全自动电热开水器', @ZLBDeptId, @KSQModelId, @HZHYSLocationId, 1, 1, GETDATE()),
        ('FN-KS-002', N'商用开水器', @ZLBDeptId, @KSQModelId, @HYSLocationId, 1, 1, GETDATE())
END
GO

-- =============================================
-- 5. 创建设备管理视图
-- =============================================

-- 设备详细信息视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_EquipmentDetails]'))
    DROP VIEW [dbo].[V_EquipmentDetails]
GO

CREATE VIEW [dbo].[V_EquipmentDetails]
AS
SELECT
    e.Id,
    e.Code,
    e.Name,
    e.SerialNumber,
    e.AssetNumber,
    e.Status,
    CASE e.Status
        WHEN 1 THEN N'正常'
        WHEN 2 THEN N'维修中'
        WHEN 3 THEN N'停用'
        WHEN 4 THEN N'报废'
        ELSE N'未知'
    END AS StatusName,
    d.Name AS DepartmentName,
    d.Code AS DepartmentCode,
    em.Name AS ModelName,
    em.Category AS ModelCategory,
    em.Brand AS ModelBrand,
    l.Name AS LocationName,
    l.Address AS LocationAddress,
    e.PurchaseDate,
    e.WarrantyExpiry,
    e.LastMaintenanceDate,
    e.NextMaintenanceDate,
    e.Description,
    e.IsEnabled,
    e.CreatedAt,
    e.Remark
FROM [dbo].[Equipment] e
    INNER JOIN [dbo].[Departments] d ON e.DepartmentId = d.Id
    INNER JOIN [dbo].[EquipmentModels] em ON e.ModelId = em.Id
    INNER JOIN [dbo].[Locations] l ON e.LocationId = l.Id
WHERE e.IsEnabled = 1
GO

-- 报修单详细信息视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderDetails]'))
    DROP VIEW [dbo].[V_RepairOrderDetails]
GO

CREATE VIEW [dbo].[V_RepairOrderDetails]
AS
SELECT
    ro.Id,
    ro.OrderNumber,
    ro.FaultDescription,
    ro.UrgencyLevel,
    CASE ro.UrgencyLevel
        WHEN 1 THEN N'紧急'
        WHEN 2 THEN N'高'
        WHEN 3 THEN N'中'
        WHEN 4 THEN N'低'
        ELSE N'未知'
    END AS UrgencyLevelName,
    ro.Status,
    CASE ro.Status
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'待确认'
        ELSE N'未知'
    END AS StatusName,
    e.Code AS EquipmentCode,
    e.Name AS EquipmentName,
    ed.Name AS EquipmentDepartmentName,
    em.Name AS EquipmentModelName,
    el.Name AS EquipmentLocationName,
    md.Name AS MaintenanceDepartmentName,
    ro.ReporterId,
    u.DisplayName AS ReporterName,
    ro.AssignedTo,
    au.DisplayName AS AssignedToName,
    ro.ReportedAt,
    ro.AssignedAt,
    ro.StartedAt,
    ro.CompletedAt,
    ro.CancelledAt,
    ro.CancelReason,
    ro.RepairDescription,
    ro.RepairCost,
    ro.PartsUsed,
    ro.TestResult,
    ro.ReporterRating,
    ro.ReporterComment,
    ro.CreatedAt,
    ro.UpdatedAt,
    ro.Remark
FROM [dbo].[RepairOrders] ro
    INNER JOIN [dbo].[Equipment] e ON ro.EquipmentId = e.Id
    INNER JOIN [dbo].[Departments] ed ON e.DepartmentId = ed.Id
    INNER JOIN [dbo].[EquipmentModels] em ON e.ModelId = em.Id
    INNER JOIN [dbo].[Locations] el ON e.LocationId = el.Id
    INNER JOIN [dbo].[Departments] md ON ro.MaintenanceDepartmentId = md.Id
    INNER JOIN [dbo].[Users] u ON ro.ReporterId = u.Id
    LEFT JOIN [dbo].[Users] au ON ro.AssignedTo = au.Id
GO

-- 零件申请详细视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartRequestDetails]'))
    DROP VIEW [dbo].[V_RepairOrderPartRequestDetails]
GO

CREATE VIEW [dbo].[V_RepairOrderPartRequestDetails]
AS
SELECT
    pr.Id,
    pr.RepairOrderId,
    ro.OrderNumber as RepairOrderNumber,
    ro.FaultDescription as RepairOrderDescription,
    pr.PartName,
    pr.Specification,
    pr.RequestedQuantity,
    pr.Unit,
    pr.Reason,
    pr.Remark,
    pr.Status,
    CASE pr.Status
        WHEN 1 THEN N'申请中'
        WHEN 2 THEN N'已领用'
        WHEN 3 THEN N'已安装'
        WHEN 4 THEN N'已取消'
        ELSE N'未知'
    END AS StatusName,
    pr.RequestedBy,
    ru.DisplayName as RequestedByName,
    pr.RequestedAt,
    pr.IssuedBy,
    iu.DisplayName as IssuedByName,
    pr.IssuedAt,
    pr.InstalledBy,
    inu.DisplayName as InstalledByName,
    pr.InstalledAt,
    pr.ExternalPartNumber,
    pr.ExternalRequisitionDetailId,
    pr.ActualQuantity,
    pr.ActualPartName,
    pr.ActualSpecification,
    pr.UnitPrice,
    pr.TotalCost,
    pr.WarehouseOrderNumber,
    pr.CreatedAt,
    pr.UpdatedAt
FROM [dbo].[RepairOrderPartRequests] pr
    INNER JOIN [dbo].[RepairOrders] ro ON pr.RepairOrderId = ro.Id
    LEFT JOIN [dbo].[Users] ru ON pr.RequestedBy = ru.Id
    LEFT JOIN [dbo].[Users] iu ON pr.IssuedBy = iu.Id
    LEFT JOIN [dbo].[Users] inu ON pr.InstalledBy = inu.Id
GO

-- 维修单零件统计视图
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairOrderPartSummary]'))
    DROP VIEW [dbo].[V_RepairOrderPartSummary]
GO

CREATE VIEW [dbo].[V_RepairOrderPartSummary]
AS
SELECT
    ro.Id as RepairOrderId,
    ro.OrderNumber,
    ro.FaultDescription as Description,
    COUNT(pr.Id) as TotalPartRequests,
    SUM(CASE WHEN pr.Status = 1 THEN 1 ELSE 0 END) as PendingRequests,
    SUM(CASE WHEN pr.Status = 2 THEN 1 ELSE 0 END) as IssuedRequests,
    SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) as InstalledRequests,
    SUM(CASE WHEN pr.Status = 4 THEN 1 ELSE 0 END) as CancelledRequests,
    SUM(pr.RequestedQuantity) as TotalRequestedQuantity,
    SUM(CASE WHEN pr.ActualQuantity IS NOT NULL THEN pr.ActualQuantity ELSE 0 END) as TotalActualQuantity,
    SUM(CASE WHEN pr.TotalCost IS NOT NULL THEN pr.TotalCost ELSE 0 END) as TotalCost,
    CASE
        WHEN COUNT(pr.Id) = 0 THEN 100
        ELSE CAST(SUM(CASE WHEN pr.Status = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(pr.Id) AS DECIMAL(5,2))
    END as CompletionPercentage
FROM [dbo].[RepairOrders] ro
    LEFT JOIN [dbo].[RepairOrderPartRequests] pr ON ro.Id = pr.RepairOrderId
GROUP BY ro.Id, ro.OrderNumber, ro.FaultDescription
GO

-- =============================================
-- 6. 创建设备管理存储过程
-- =============================================

-- 生成报修单号的存储过程
IF EXISTS (SELECT * FROM sys.procedures WHERE object_id = OBJECT_ID(N'[dbo].[sp_GenerateRepairOrderNumber]'))
    DROP PROCEDURE [dbo].[sp_GenerateRepairOrderNumber]
GO

CREATE PROCEDURE [dbo].[sp_GenerateRepairOrderNumber]
    @OrderNumber NVARCHAR(50) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Today NVARCHAR(8) = CONVERT(NVARCHAR(8), GETDATE(), 112)
    DECLARE @Prefix NVARCHAR(10) = 'RO' + @Today
    DECLARE @MaxSeq INT = 0

    -- 获取今天的最大序号
    SELECT @MaxSeq = ISNULL(MAX(CAST(RIGHT(OrderNumber, 3) AS INT)), 0)
    FROM [dbo].[RepairOrders]
    WHERE OrderNumber LIKE @Prefix + '%'

    -- 生成新的序号
    SET @MaxSeq = @MaxSeq + 1
    SET @OrderNumber = @Prefix + RIGHT('000' + CAST(@MaxSeq AS NVARCHAR(3)), 3)
END
GO

-- =============================================
-- 7. 创建工作流历史表 (RepairWorkflowHistory)
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RepairWorkflowHistory]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[RepairWorkflowHistory](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [RepairOrderId] [int] NOT NULL,
        [UserId] [int] NOT NULL,
        [Action] [nvarchar](100) NOT NULL,
        [Comment] [nvarchar](1000) NULL,
        [FromStatus] [int] NULL,
        [ToStatus] [int] NULL,
        [CreatedAt] [datetime] NOT NULL DEFAULT(GETDATE()),
        [AdditionalData] [nvarchar](max) NULL, -- JSON格式存储额外数据
        CONSTRAINT [PK_RepairWorkflowHistory] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_RepairWorkflowHistory_RepairOrders] FOREIGN KEY([RepairOrderId]) REFERENCES [dbo].[RepairOrders] ([Id]),
        CONSTRAINT [FK_RepairWorkflowHistory_Users] FOREIGN KEY([UserId]) REFERENCES [dbo].[Users] ([Id])
    )
END
GO

-- 创建索引以提高查询性能
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairWorkflowHistory]') AND name = N'IX_RepairWorkflowHistory_RepairOrderId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairWorkflowHistory_RepairOrderId] ON [dbo].[RepairWorkflowHistory]
    (
        [RepairOrderId] ASC
    )
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[RepairWorkflowHistory]') AND name = N'IX_RepairWorkflowHistory_CreatedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_RepairWorkflowHistory_CreatedAt] ON [dbo].[RepairWorkflowHistory]
    (
        [CreatedAt] DESC
    )
END
GO

-- =============================================
-- 8. 创建工作流历史视图 (V_RepairWorkflowHistoryDetails)
-- =============================================
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[V_RepairWorkflowHistoryDetails]'))
    DROP VIEW [dbo].[V_RepairWorkflowHistoryDetails]
GO

CREATE VIEW [dbo].[V_RepairWorkflowHistoryDetails]
AS
SELECT
    rwh.Id,
    rwh.RepairOrderId,
    ro.OrderNumber,
    rwh.UserId,
    u.DisplayName AS UserName,
    u.Username AS UserLoginName,
    rwh.Action,
    rwh.Comment,
    rwh.FromStatus,
    rwh.ToStatus,
    CASE rwh.FromStatus
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'待确认'
        ELSE N'未知'
    END AS FromStatusName,
    CASE rwh.ToStatus
        WHEN 1 THEN N'待处理'
        WHEN 2 THEN N'处理中'
        WHEN 3 THEN N'已完成'
        WHEN 4 THEN N'已作废'
        WHEN 5 THEN N'已关闭'
        WHEN 6 THEN N'待确认'
        ELSE N'未知'
    END AS ToStatusName,
    rwh.CreatedAt,
    rwh.AdditionalData,
    e.Name AS EquipmentName,
    e.Code AS EquipmentCode
FROM [dbo].[RepairWorkflowHistory] rwh
    INNER JOIN [dbo].[RepairOrders] ro ON rwh.RepairOrderId = ro.Id
    INNER JOIN [dbo].[Users] u ON rwh.UserId = u.Id
    INNER JOIN [dbo].[Equipment] e ON ro.EquipmentId = e.Id
GO

-- =============================================
-- 9. 创建工作流历史存储过程
-- =============================================

-- 添加工作流历史记录的存储过程
IF EXISTS (SELECT * FROM sys.procedures WHERE object_id = OBJECT_ID(N'[dbo].[sp_AddWorkflowHistory]'))
    DROP PROCEDURE [dbo].[sp_AddWorkflowHistory]
GO

CREATE PROCEDURE [dbo].[sp_AddWorkflowHistory]
    @RepairOrderId INT,
    @UserId INT,
    @Action NVARCHAR(100),
    @Comment NVARCHAR(1000) = NULL,
    @FromStatus INT = NULL,
    @ToStatus INT = NULL,
    @AdditionalData NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        INSERT INTO [dbo].[RepairWorkflowHistory]
        (
            RepairOrderId,
            UserId,
            Action,
            Comment,
            FromStatus,
            ToStatus,
            AdditionalData
        )
        VALUES
        (
            @RepairOrderId,
            @UserId,
            @Action,
            @Comment,
            @FromStatus,
            @ToStatus,
            @AdditionalData
        );

        SELECT SCOPE_IDENTITY() AS NewId;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END
GO

-- 获取报修单工作流历史的存储过程
IF EXISTS (SELECT * FROM sys.procedures WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetRepairWorkflowHistory]'))
    DROP PROCEDURE [dbo].[sp_GetRepairWorkflowHistory]
GO

CREATE PROCEDURE [dbo].[sp_GetRepairWorkflowHistory]
    @RepairOrderId INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        Id,
        RepairOrderId,
        OrderNumber,
        UserId,
        UserName,
        UserLoginName,
        Action,
        Comment,
        FromStatus,
        ToStatus,
        FromStatusName,
        ToStatusName,
        CreatedAt,
        AdditionalData,
        EquipmentName,
        EquipmentCode
    FROM [dbo].[V_RepairWorkflowHistoryDetails]
    WHERE RepairOrderId = @RepairOrderId
    ORDER BY CreatedAt DESC;
END
GO

-- =============================================
-- 10. 插入示例工作流历史数据
-- =============================================

-- 获取第一个报修单ID和用户ID用于示例数据
DECLARE @SampleRepairOrderId INT;
DECLARE @AdminUserId INT;
DECLARE @ViewerUserId INT;

SELECT TOP 1 @SampleRepairOrderId = Id FROM RepairOrders ORDER BY Id;
SELECT @AdminUserId = Id FROM Users WHERE Username = 'admin';
SELECT @ViewerUserId = Id FROM Users WHERE Username = 'viewer';

-- 只有在找到相关数据时才插入示例历史记录
IF @SampleRepairOrderId IS NOT NULL AND @AdminUserId IS NOT NULL AND @ViewerUserId IS NOT NULL
BEGIN
    -- 清理可能存在的旧数据
    DELETE FROM RepairWorkflowHistory WHERE RepairOrderId = @SampleRepairOrderId;

    -- 插入示例工作流历史记录
    INSERT INTO RepairWorkflowHistory (RepairOrderId, UserId, Action, Comment, FromStatus, ToStatus, CreatedAt)
    VALUES
    (@SampleRepairOrderId, @ViewerUserId, '创建报修单', '用户提交了新的报修申请', NULL, 1, DATEADD(HOUR, -24, GETDATE())),
    (@SampleRepairOrderId, @AdminUserId, '分配维修人员', '将报修单分配给维修技术员', 1, 1, DATEADD(HOUR, -23, GETDATE())),
    (@SampleRepairOrderId, @AdminUserId, '接受维修任务', '维修技术员接受了维修任务', 1, 2, DATEADD(HOUR, -22, GETDATE())),
    (@SampleRepairOrderId, @AdminUserId, '开始维修工作', '开始对设备进行检查和维修', 1, 2, DATEADD(HOUR, -21, GETDATE())),
    (@SampleRepairOrderId, @AdminUserId, '暂停维修工作', '等待配件到货', 2, 6, DATEADD(HOUR, -12, GETDATE())),
    (@SampleRepairOrderId, @AdminUserId, '恢复维修工作', '配件已到货，继续维修', 6, 2, DATEADD(HOUR, -6, GETDATE())),
    (@SampleRepairOrderId, @AdminUserId, '完成维修工作', '维修工作已完成，设备测试正常', 2, 3, DATEADD(HOUR, -2, GETDATE()));

    PRINT '已插入示例工作流历史记录: ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + ' 条';
END
ELSE
BEGIN
    PRINT '未找到合适的数据，跳过示例工作流历史记录插入';
END

PRINT '==============================================';
PRINT '企业管理系统数据库脚本执行完成！';
PRINT '==============================================';
PRINT '';
PRINT '权限管理系统：';
PRINT '  用户数据: 3条';
PRINT '  角色数据: 3条';
PRINT '  权限数据: 50条';
PRINT '  菜单数据: 完整菜单结构';
PRINT '';
PRINT '设备管理系统：';
PRINT '  部门数据: 4条';
PRINT '  设备型号数据: 26条';
PRINT '  位置数据: 9条';
PRINT '  设备数据: 66条';
PRINT '  视图: 3个 (设备详情、报修单详情、工作流历史详情)';
PRINT '  存储过程: 3个 (报修单号生成、工作流历史管理)';
PRINT '';
PRINT '工作流历史系统：';
PRINT '  工作流历史表: RepairWorkflowHistory';
PRINT '  历史详情视图: V_RepairWorkflowHistoryDetails';
PRINT '  历史管理存储过程: sp_AddWorkflowHistory, sp_GetRepairWorkflowHistory';
PRINT '';
PRINT '测试账号：';
PRINT '  管理员: admin / admin123 (全部权限)';
PRINT '  操作员: operator / op123 (设备管理权限)';
PRINT '  访客: viewer / view123 (基础查看权限)';
PRINT '';
PRINT '数据库名称: EnterpriseManagementSystem';
PRINT '注意：当前使用明文密码存储，仅适用于开发环境！';
PRINT '';
PRINT '零件申请表特性：';
PRINT '- 无审批流程，简化的4状态流转';
PRINT '- 状态：1=申请中, 2=已领用, 3=已安装, 4=已取消';
PRINT '- 流程：申请中 → 已领用 → 已安装';
PRINT '- 支持外部系统集成';
PRINT '- 包含完整的索引和视图';
PRINT '';
PRINT '报修单状态特性：';
PRINT '- 连续状态值设计：1-6';
PRINT '- 状态：1=待处理, 2=处理中, 3=已完成, 4=已作废, 5=已关闭, 6=待确认';
PRINT '- 移除了"已暂停"状态，简化业务流程';
PRINT '- 状态流转：待处理 → 处理中 → 待确认 → 已完成 → 已关闭';
PRINT '==============================================';
GO