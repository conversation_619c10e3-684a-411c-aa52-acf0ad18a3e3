-- =============================================
-- 补充清理剩余测试菜单脚本
-- 用于删除第一次清理后仍然存在的测试菜单
-- =============================================

USE [EnterpriseManagementSystem]
GO

PRINT '开始补充清理剩余测试菜单...'
PRINT '========================================'

-- 首先查看当前剩余的可疑菜单
PRINT '当前剩余的可疑测试菜单：'
SELECT 
    m.Id,
    m.Code AS '菜单代码',
    m.Name AS '菜单名称',
    m.RouteUrl AS '路由地址',
    m.Icon AS '图标'
FROM MenuItems m
WHERE 
    m.Name LIKE '%测试%' 
    OR m.Name LIKE '%调试%' 
    OR m.Name LIKE '%修复%'
    OR m.Name LIKE '%工作流%'
    OR m.Name IN (
        '修复维修权限',
        '测试维修部门', 
        '工作流历史测试',
        '角色部门权限管理',
        '认证调试',
        '用户部门测试',
        '维修权限调试'
    )
ORDER BY m.Name;

-- 开始删除操作
BEGIN TRANSACTION;

BEGIN TRY
    DECLARE @DeletedCount INT = 0;
    
    -- 方法1：按名称模式删除
    DELETE FROM MenuItems 
    WHERE 
        Name LIKE '%测试%' 
        OR Name LIKE '%调试%' 
        OR Name LIKE '%修复%'
        OR Name LIKE '%工作流%历史%'
        OR Name IN (
            '修复维修权限',
            '测试维修部门', 
            '工作流历史测试',
            '角色部门权限管理',
            '认证调试',
            '用户部门测试',
            '维修权限调试'
        );
    
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    -- 方法2：按代码模式删除（如果有的话）
    DELETE FROM MenuItems 
    WHERE 
        Code LIKE '%Test%'
        OR Code LIKE '%Debug%'
        OR Code LIKE '%Fix%'
        OR Code LIKE '%Workflow%'
        OR Code IN (
            'FixMaintenancePermission',
            'TestMaintenanceDepartment',
            'WorkflowHistoryTest',
            'RoleDepartmentPermissionManagement',
            'DebugAuth',
            'TestUserDepartment',
            'DebugMaintenancePermissions'
        );
    
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    -- 方法3：按路由模式删除
    DELETE FROM MenuItems 
    WHERE 
        RouteUrl LIKE '%test%'
        OR RouteUrl LIKE '%debug%'
        OR RouteUrl LIKE '%fix%'
        OR RouteUrl LIKE '%workflow%'
        OR RouteUrl IN (
            'fix-maintenance-permission',
            'test-maintenance-department',
            'workflow-history-test',
            'role-department-permission-management',
            'debug-auth',
            'test-user-department',
            'debug-maintenance-permissions'
        );
    
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    COMMIT TRANSACTION;
    
    PRINT ''
    PRINT '补充清理完成！'
    PRINT '总共删除了 ' + CAST(@DeletedCount AS NVARCHAR(10)) + ' 个菜单项'
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    
    PRINT ''
    PRINT '补充清理失败：' + ERROR_MESSAGE()
    
END CATCH

-- 验证清理结果
PRINT ''
PRINT '验证补充清理结果：'

-- 检查是否还有测试菜单残留
DECLARE @RemainingTestMenus INT;
SELECT @RemainingTestMenus = COUNT(*)
FROM MenuItems m
WHERE 
    m.Name LIKE '%测试%' 
    OR m.Name LIKE '%调试%' 
    OR m.Name LIKE '%修复%'
    OR m.Name LIKE '%工作流%'
    OR m.Name IN (
        '修复维修权限',
        '测试维修部门', 
        '工作流历史测试'
    );

IF @RemainingTestMenus = 0
BEGIN
    PRINT '✓ 所有测试菜单已成功删除'
END
ELSE
BEGIN
    PRINT '⚠ 仍有 ' + CAST(@RemainingTestMenus AS NVARCHAR(10)) + ' 个测试菜单未删除'
    
    -- 显示未删除的菜单
    PRINT '未删除的菜单：'
    SELECT 
        m.Id,
        m.Code AS '菜单代码',
        m.Name AS '菜单名称',
        m.RouteUrl AS '路由地址',
        '可能需要手动删除' AS '建议'
    FROM MenuItems m
    WHERE 
        m.Name LIKE '%测试%' 
        OR m.Name LIKE '%调试%' 
        OR m.Name LIKE '%修复%'
        OR m.Name LIKE '%工作流%'
        OR m.Name IN (
            '修复维修权限',
            '测试维修部门', 
            '工作流历史测试'
        );
END

-- 显示清理后的菜单结构
PRINT ''
PRINT '清理后的菜单结构：'
SELECT 
    CASE 
        WHEN m.Level = 1 THEN m.Name
        WHEN m.Level = 2 THEN '  ├── ' + m.Name
        WHEN m.Level = 3 THEN '    ├── ' + m.Name
        ELSE REPLICATE('  ', m.Level - 1) + '├── ' + m.Name
    END AS '菜单结构',
    m.RouteUrl AS '路由',
    CASE m.IsEnabled 
        WHEN 1 THEN '启用' 
        ELSE '禁用' 
    END AS '状态'
FROM MenuItems m
WHERE m.IsEnabled = 1
ORDER BY 
    COALESCE(m.ParentId, m.Id),
    m.SortOrder,
    m.Id;

-- 如果还有顽固的菜单，提供手动删除的SQL
PRINT ''
PRINT '如果仍有菜单未删除，可以使用以下SQL手动删除：'
PRINT ''

-- 生成手动删除SQL
DECLARE @ManualDeleteSQL NVARCHAR(MAX) = '';
SELECT @ManualDeleteSQL = @ManualDeleteSQL + 
    'DELETE FROM MenuItems WHERE Id = ' + CAST(m.Id AS NVARCHAR(10)) + '; -- ' + m.Name + CHAR(13) + CHAR(10)
FROM MenuItems m
WHERE 
    m.Name LIKE '%测试%' 
    OR m.Name LIKE '%调试%' 
    OR m.Name LIKE '%修复%'
    OR m.Name LIKE '%工作流%'
    OR m.Name IN (
        '修复维修权限',
        '测试维修部门', 
        '工作流历史测试'
    );

IF LEN(@ManualDeleteSQL) > 0
BEGIN
    PRINT @ManualDeleteSQL;
END
ELSE
BEGIN
    PRINT '-- 没有需要手动删除的菜单'
END

PRINT ''
PRINT '========================================'
PRINT '补充清理完成！'

GO
