-- =============================================
-- 清理测试菜单脚本
-- 用于删除开发和测试阶段创建的临时菜单项
-- =============================================

USE [EnterpriseManagementSystem]
GO

PRINT '开始清理测试菜单...'
PRINT '========================================'

-- 要删除的测试菜单代码列表
DECLARE @TestMenuCodes TABLE (Code NVARCHAR(50))
INSERT INTO @TestMenuCodes VALUES
    ('RoleDepartmentPermissionManagement'),  -- 角色部门权限管理
    ('DebugAuth'),                          -- 认证调试
    ('TestUserDepartment'),                 -- 用户部门测试
    ('DebugMaintenancePermissions'),        -- 维修权限调试
    ('FixMaintenancePermission'),           -- 修复维修权限
    ('TestMaintenanceDepartment'),          -- 测试维修部门
    ('WorkflowHistoryTest'),                -- 工作流历史测试
    ('CameraTest'),                         -- 摄像头测试
    ('Counter'),                            -- 计数器
    ('Weather'),                            -- 天气预报
    ('DeviceScanner'),                      -- 设备扫描
    ('DeviceRepair'),                       -- 设备报修（旧版本）
    ('AuthorizeViewExample');               -- 权限控制示例

PRINT '准备删除的测试菜单：'

-- 显示将要删除的菜单
SELECT 
    m.Code AS '菜单代码',
    m.Name AS '菜单名称',
    m.RouteUrl AS '路由地址',
    CASE m.IsSystem 
        WHEN 1 THEN '是' 
        ELSE '否' 
    END AS '系统菜单'
FROM MenuItems m
INNER JOIN @TestMenuCodes t ON m.Code = t.Code
ORDER BY m.Name;

-- 检查是否有子菜单需要一起删除
PRINT ''
PRINT '检查子菜单：'
SELECT 
    child.Code AS '子菜单代码',
    child.Name AS '子菜单名称',
    parent.Name AS '父菜单名称'
FROM MenuItems child
INNER JOIN MenuItems parent ON child.ParentId = parent.Id
INNER JOIN @TestMenuCodes t ON parent.Code = t.Code
ORDER BY parent.Name, child.Name;

-- 开始删除操作
BEGIN TRANSACTION;

BEGIN TRY
    DECLARE @DeletedCount INT = 0;
    
    -- 首先删除子菜单（如果有的话）
    DELETE child 
    FROM MenuItems child
    INNER JOIN MenuItems parent ON child.ParentId = parent.Id
    INNER JOIN @TestMenuCodes t ON parent.Code = t.Code;
    
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    -- 然后删除主菜单
    DELETE m 
    FROM MenuItems m
    INNER JOIN @TestMenuCodes t ON m.Code = t.Code;
    
    SET @DeletedCount = @DeletedCount + @@ROWCOUNT;
    
    COMMIT TRANSACTION;
    
    PRINT ''
    PRINT '清理完成！'
    PRINT '总共删除了 ' + CAST(@DeletedCount AS NVARCHAR(10)) + ' 个菜单项'
    
END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION;
    
    PRINT ''
    PRINT '清理失败：' + ERROR_MESSAGE()
    
END CATCH

-- 验证清理结果
PRINT ''
PRINT '验证清理结果：'

-- 检查是否还有测试菜单残留
DECLARE @RemainingCount INT;
SELECT @RemainingCount = COUNT(*)
FROM MenuItems m
INNER JOIN @TestMenuCodes t ON m.Code = t.Code;

IF @RemainingCount = 0
BEGIN
    PRINT '✓ 所有测试菜单已成功删除'
END
ELSE
BEGIN
    PRINT '⚠ 仍有 ' + CAST(@RemainingCount AS NVARCHAR(10)) + ' 个测试菜单未删除'
    
    -- 显示未删除的菜单
    SELECT 
        m.Code AS '未删除的菜单代码',
        m.Name AS '菜单名称',
        '可能有外键约束或其他依赖' AS '原因'
    FROM MenuItems m
    INNER JOIN @TestMenuCodes t ON m.Code = t.Code;
END

-- 显示当前剩余的菜单结构
PRINT ''
PRINT '当前菜单结构：'
SELECT 
    CASE 
        WHEN m.Level = 1 THEN m.Name
        WHEN m.Level = 2 THEN '  ├── ' + m.Name
        WHEN m.Level = 3 THEN '    ├── ' + m.Name
        ELSE REPLICATE('  ', m.Level - 1) + '├── ' + m.Name
    END AS '菜单结构',
    m.RouteUrl AS '路由',
    CASE m.IsEnabled 
        WHEN 1 THEN '启用' 
        ELSE '禁用' 
    END AS '状态'
FROM MenuItems m
WHERE m.IsEnabled = 1
ORDER BY 
    COALESCE(m.ParentId, m.Id),
    m.SortOrder,
    m.Id;

PRINT ''
PRINT '========================================'
PRINT '测试菜单清理完成！'
PRINT ''
PRINT '建议后续操作：'
PRINT '1. 重启应用程序以刷新菜单缓存'
PRINT '2. 检查是否有对应的页面文件需要删除'
PRINT '3. 清理相关的权限配置（如果有的话）'
PRINT '4. 更新菜单管理文档'

GO
